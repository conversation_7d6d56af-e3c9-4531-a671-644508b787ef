"""
Core document analyzer with different analysis modes.
"""

import json
from typing import Dict, Any, List, Optional, Literal
from dataclasses import dataclass
from ..core.schema import AnalysisResult, ActorInfo, StakeholderCategory
from ..utils.validator import JSONValidator

@dataclass
class AnalysisRequest:
    doc_id: str
    title: Optional[str]
    text: str
    optional_known_actors: Optional[List[str]] = None

class PromptTemplates:
    """Stores all prompt templates for different analysis modes."""
    
    # Core system definitions - shared across all prompts
    CORE_DEFINITIONS = """
CRITICAL DEFINITIONS
- Actors: Real persons or organizations only. No abstract/virtual roles
- Relationship: Directed/undirected interaction between two actors (e.g., "regulates", "lobbies", "collaborates", "funds", "criticizes")
- Portrayals:
  • Hero: Actor self-frames (or is framed) as able to fix problems, self-promotional, emphasizing protective/solution role
  • Victim: Actor framed as harmed or bearing consequences from narratives/actions, not merely from policy limitations
  • Devil: Opponents framed as malicious or more evil than they are, exaggerating motives/harms
- Issue expansion: Deliberate broadening of scope/audience, diffusing costs across many while benefits concentrate to few
- Issue containment: Deliberate narrowing of scope/audience to specialists, limiting salience
- Causal mechanisms:
  • Intentional: Assigns blame deliberately to harm others' reputations or shift responsibility
  • Inadvertent: Attributes problems to unintended consequences of a policy
- Stakeholder categories: ["Think Tanks","Government","Media","Corporate","NGOs and Professional Organizations","Universities and Research Institutes","Political Entities","Consultants and Analysts","Legal and Industry-specific Bodies","State Guidelines and Documents","Others"]

EXTRACTION REQUIREMENTS
- Extract specific, named actors (e.g., "FTC", "OpenAI", "NYT"); avoid categories like "policymakers" unless verbatim
- Maintain directional consistency for one-way relationships (e.g., "A regulates B")
- Split multi-actor mentions into multiple pairwise rows
- Use concise verb phrases for relationships
- Evidence must be direct quotations or tight paraphrases tied to exact text spans
- If a section has no valid findings, set skipped=true and provide skip_reason

EXCLUSION RULES (exclude from findings)
- Non-real/virtual roles; hypothetical/suggestive/precautionary statements ("should", "could" without realized framing)
- Flat factual descriptions of current policy without narrative framing
- Generic concerns without named actors; indirect/implicit framings that cannot be grounded in text
- Pure insights/opinions from the analyst; drawbacks that are not devil/hero/victim framing
- Statements without blame for causal mechanisms
"""

    VALIDATION_REQUIREMENTS = """
VALIDATION REQUIREMENTS
- Return valid JSON only; no trailing commas; correct enums; nulls where required
- All actor references in relationships/portrayals/issues/causals must exist in actors[] (add via ai_decisions if needed)
"""

    # Unified comprehensive analysis prompt
    UNIFIED_PROMPT = f"""You are an information extraction and narrative analysis model. Read the input document and return ONLY valid JSON matching the Output Schema. Do not include explanations outside the JSON. Follow all constraints exactly.

GOALS
1) Extract actors (only real persons or organizations), their pairwise relationships, and types
2) Detect portrayals (hero, victim, devil) with evidence
3) Detect issue scope strategies (issue expansion, issue containment) with evidence
4) Detect causal mechanisms (intentional blame, inadvertent consequences) with evidence
5) Provide add/remove actor decisions if warranted

INPUT PARAMETERS
- doc_id: string (if missing, use "000")
- title: string or null
- text: string (the document body)
- optional_known_actors: array of strings (may be empty)

{CORE_DEFINITIONS}

OUTPUT SCHEMA
{{
  "doc_id": "string",
  "title": "string|null",
  "actors": [
    {{
      "name": "string",
      "stakeholder_category": "Think Tanks|Government|Media|Corporate|NGOs and Professional Organizations|Universities and Research Institutes|Political Entities|Consultants and Analysts|Legal and Industry-specific Bodies|State Guidelines and Documents|Others",
      "notes": "string|null"
    }}
  ],
  "relationships": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "a_name": "string",
        "b_name": "string",
        "relationship": "string", 
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null",
        "a_type": "stakeholder category from list|Others",
        "b_type": "stakeholder category from list|Others",
        "evidence": "quote or concise span from text"
      }}
    ]
  }},
  "portrayals": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "quote",
        "explanation": "1-2 sentences grounding why this is Hero/Victim/Devil"
      }}
    ],
    "shifts": {{
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean",
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }}
  }},
  "issue_scope": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "actor": "string",
        "type": "Issue expansion|Issue containment",
        "evidence": "quote",
        "explanation": "1-2 sentences explaining the deliberate broadening/narrowing"
      }}
    ]
  }},
  "causal_mechanisms": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {{
        "actor": "string",
        "type": "Intentional|Inadvertent",
        "evidence": "quote or short passage (can span multiple sentences)",
        "explanation": "1-2 sentences on blame assignment or unintended consequences"
      }}
    ]
  }},
  "ai_decisions": [
    {{
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }}
  ]
}}

{VALIDATION_REQUIREMENTS}

Now analyze the document:

doc_id: {{doc_id}}
title: {{title}}
text: |
{{text}}
optional_known_actors: {{optional_known_actors}}"""

    # Task-specific prompts for focused analysis
    ENTITY_RELATIONSHIPS_PROMPT = f"""Task: Extract actors and pairwise relationships.
Follow DEFINITIONS/REQUIREMENTS/Exclusions above. Return only:
{{
  "doc_id": "string",
  "title": "string|null",
  "actors": [{{"name": "string", "stakeholder_category": "...", "notes": "string|null"}}],
  "relationships": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [{{
      "a_name":"string","b_name":"string","relationship":"string",
      "a_character":"hero|villain|victim|null","b_character":"hero|villain|victim|null",
      "a_type":"...","b_type":"...","evidence":"string"
    }}]
  }}
}}

{CORE_DEFINITIONS}

{VALIDATION_REQUIREMENTS}

Input: doc_id: {{doc_id}}, title: {{title}}, text: {{text}}, optional_known_actors: {{optional_known_actors}}"""

    PORTRAYALS_PROMPT = f"""Task: Identify hero/victim/devil portrayals and assess angel/devil shift.
Follow DEFINITIONS/REQUIREMENTS/Exclusions above. Return only:
{{
  "portrayals": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [{{"actor":"string","type":"Hero|Victim|Devil","evidence":"quote","explanation":"string"}}],
    "shifts": {{
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean",
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }}
  }},
  "ai_decisions": [{{"action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string"}}]
}}

{CORE_DEFINITIONS}

{VALIDATION_REQUIREMENTS}

Input: doc_id: {{doc_id}}, title: {{title}}, text: {{text}}, optional_known_actors: {{optional_known_actors}}"""

    ISSUE_SCOPE_PROMPT = f"""Task: Identify issue expansion/containment narratives.
Return only:
{{
  "issue_scope": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [{{"actor":"string","type":"Issue expansion|Issue containment","evidence":"quote","explanation":"string"}}]
  }},
  "ai_decisions": [{{"action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string"}}]
}}

{CORE_DEFINITIONS}

{VALIDATION_REQUIREMENTS}

Input: doc_id: {{doc_id}}, title: {{title}}, text: {{text}}, optional_known_actors: {{optional_known_actors}}"""

    CAUSAL_MECHANISMS_PROMPT = f"""Task: Identify intentional and/or inadvertent causal mechanisms.
Return only:
{{
  "causal_mechanisms": {{
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [{{"actor":"string","type":"Intentional|Inadvertent","evidence":"quote or short passage","explanation":"string"}}]
  }},
  "ai_decisions": [{{"action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string"}}]
}}

{CORE_DEFINITIONS}

{VALIDATION_REQUIREMENTS}

Input: doc_id: {{doc_id}}, title: {{title}}, text: {{text}}, optional_known_actors: {{optional_known_actors}}"""

class DocumentAnalyzer:
    """Main document analysis engine."""
    
    def __init__(self):
        self.validator = JSONValidator()
        self.templates = PromptTemplates()
    
    def create_analysis_request(
        self, 
        doc_id: str, 
        title: Optional[str], 
        text: str, 
        optional_known_actors: Optional[List[str]] = None
    ) -> AnalysisRequest:
        """Create an analysis request object."""
        return AnalysisRequest(
            doc_id=doc_id or "000",
            title=title,
            text=text,
            optional_known_actors=optional_known_actors or []
        )
    
    def format_prompt(
        self, 
        template: str, 
        request: AnalysisRequest
    ) -> str:
        """Format a prompt template with request data."""
        return template.format(
            doc_id=request.doc_id,
            title=json.dumps(request.title),
            text=request.text,
            optional_known_actors=json.dumps(request.optional_known_actors)
        )
    
    def get_unified_prompt(self, request: AnalysisRequest) -> str:
        """Get the unified comprehensive analysis prompt."""
        return self.format_prompt(self.templates.UNIFIED_PROMPT, request)
    
    def get_entity_relationships_prompt(self, request: AnalysisRequest) -> str:
        """Get the entity and relationship extraction prompt."""
        return self.format_prompt(self.templates.ENTITY_RELATIONSHIPS_PROMPT, request)
    
    def get_portrayals_prompt(self, request: AnalysisRequest) -> str:
        """Get the portrayals analysis prompt."""
        return self.format_prompt(self.templates.PORTRAYALS_PROMPT, request)
    
    def get_issue_scope_prompt(self, request: AnalysisRequest) -> str:
        """Get the issue scope analysis prompt.""" 
        return self.format_prompt(self.templates.ISSUE_SCOPE_PROMPT, request)
    
    def get_causal_mechanisms_prompt(self, request: AnalysisRequest) -> str:
        """Get the causal mechanisms analysis prompt."""
        return self.format_prompt(self.templates.CAUSAL_MECHANISMS_PROMPT, request)
    
    def validate_analysis_result(
        self, 
        json_response: str, 
        document_text: str
    ) -> tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Validate an analysis result JSON response.
        
        Args:
            json_response: JSON string response from analysis
            document_text: Original document text for evidence validation
            
        Returns:
            Tuple of (is_valid, error_message, parsed_data)
        """
        # First validate JSON structure
        is_valid, error, data = self.validator.validate_json_string(json_response)
        if not is_valid:
            return False, error, None
        
        # Check actor references
        missing_actors = self.validator.check_actor_references(data)
        if missing_actors:
            return False, f"Missing actor references: {', '.join(missing_actors[:5])}", None
        
        # Check evidence quotes (optional - may not want to enforce strictly)
        # missing_evidence = self.validator.validate_evidence_quotes(data, document_text)
        # if missing_evidence:
        #     return False, f"Evidence not found in document: {', '.join(missing_evidence[:3])}", None
        
        return True, None, data
    
    def analyze_document(
        self, 
        request: AnalysisRequest,
        mode: Literal["unified", "entities", "portrayals", "issues", "causal"] = "unified"
    ) -> str:
        """
        Generate analysis prompt for a document.
        
        Args:
            request: Analysis request containing document data
            mode: Analysis mode to use
            
        Returns:
            Formatted prompt string ready for LLM processing
        """
        if mode == "unified":
            return self.get_unified_prompt(request)
        elif mode == "entities":
            return self.get_entity_relationships_prompt(request)
        elif mode == "portrayals":
            return self.get_portrayals_prompt(request)
        elif mode == "issues":
            return self.get_issue_scope_prompt(request)
        elif mode == "causal":
            return self.get_causal_mechanisms_prompt(request)
        else:
            raise ValueError(f"Unknown analysis mode: {mode}")
    
    def process_document_batch(
        self, 
        documents: List[tuple[str, Optional[str], str]], 
        known_actors: Optional[List[str]] = None,
        mode: str = "unified"
    ) -> List[str]:
        """
        Process a batch of documents and return prompts.
        
        Args:
            documents: List of (doc_id, title, text) tuples
            known_actors: Optional list of known actors to include
            mode: Analysis mode
            
        Returns:
            List of formatted prompts
        """
        prompts = []
        for doc_id, title, text in documents:
            request = self.create_analysis_request(doc_id, title, text, known_actors)
            prompt = self.analyze_document(request, mode)
            prompts.append(prompt)
        return prompts