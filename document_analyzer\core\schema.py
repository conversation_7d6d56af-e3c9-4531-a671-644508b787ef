"""
Core data structures and schema definitions for document analysis.
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Union, Literal
from enum import Enum

class StakeholderCategory(Enum):
    THINK_TANKS = "Think Tanks"
    GOVERNMENT = "Government"
    MEDIA = "Media"
    CORPORATE = "Corporate"
    NGOS_PROFESSIONAL = "NGOs and Professional Organizations"
    UNIVERSITIES_RESEARCH = "Universities and Research Institutes"
    POLITICAL_ENTITIES = "Political Entities"
    CONSULTANTS_ANALYSTS = "Consultants and Analysts"
    LEGAL_INDUSTRY = "Legal and Industry-specific Bodies"
    STATE_GUIDELINES = "State Guidelines and Documents"
    OTHERS = "Others"

class PortrayalType(Enum):
    HERO = "Hero"
    VICTIM = "Victim"
    DEVIL = "Devil"

class IssueScopeType(Enum):
    EXPANSION = "Issue expansion"
    CONTAINMENT = "Issue containment"

class CausalMechanismType(Enum):
    INTENTIONAL = "Intentional"
    INADVERTENT = "Inadvertent"

class CharacterType(Enum):
    HERO = "hero"
    VILLAIN = "villain"
    VICTIM = "victim"

@dataclass
class ActorInfo:
    name: str
    stakeholder_category: StakeholderCategory
    notes: Optional[str] = None

@dataclass
class RelationshipInfo:
    a_name: str
    b_name: str
    relationship: str
    a_character: Optional[CharacterType]
    b_character: Optional[CharacterType]
    a_type: StakeholderCategory
    b_type: StakeholderCategory
    evidence: str

@dataclass
class PortrayalInfo:
    actor: str
    type: PortrayalType
    evidence: str
    explanation: str

@dataclass
class PortrayalShifts:
    angel_shift_present: bool
    devil_shift_present: bool
    angel_shift_evidence: Optional[str] = None
    devil_shift_evidence: Optional[str] = None

@dataclass
class IssueScopeInfo:
    actor: str
    type: IssueScopeType
    evidence: str
    explanation: str

@dataclass
class CausalMechanismInfo:
    actor: str
    type: CausalMechanismType
    evidence: str
    explanation: str

@dataclass
class AIDecision:
    action: Literal["add_actor", "remove_actor", "none"]
    actor: Optional[str]
    reasoning: str

@dataclass
class AnalysisSection:
    skipped: bool
    skip_reason: Optional[str]
    items: List[Any]

@dataclass
class RelationshipsSection(AnalysisSection):
    items: List[RelationshipInfo]

@dataclass
class PortrayalsSection(AnalysisSection):
    items: List[PortrayalInfo]
    shifts: PortrayalShifts

@dataclass
class IssueScopeSection(AnalysisSection):
    items: List[IssueScopeInfo]

@dataclass
class CausalMechanismsSection(AnalysisSection):
    items: List[CausalMechanismInfo]

@dataclass
class AnalysisResult:
    doc_id: str
    title: Optional[str]
    actors: List[ActorInfo]
    relationships: RelationshipsSection
    portrayals: PortrayalsSection
    issue_scope: IssueScopeSection
    causal_mechanisms: CausalMechanismsSection
    ai_decisions: List[AIDecision]

    def to_dict(self) -> Dict[str, Any]:
        """Convert the analysis result to a dictionary format."""
        def convert_enum(value):
            if isinstance(value, Enum):
                return value.value
            return value

        def convert_dataclass(obj):
            if hasattr(obj, '__dict__'):
                result = {}
                for key, value in obj.__dict__.items():
                    if isinstance(value, list):
                        result[key] = [convert_dataclass(item) if hasattr(item, '__dict__') else convert_enum(item) for item in value]
                    elif hasattr(value, '__dict__'):
                        result[key] = convert_dataclass(value)
                    else:
                        result[key] = convert_enum(value)
                return result
            return convert_enum(obj)

        return convert_dataclass(self)

# JSON Schema for validation
ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "doc_id": {"type": "string"},
        "title": {"type": ["string", "null"]},
        "actors": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "stakeholder_category": {
                        "type": "string",
                        "enum": [cat.value for cat in StakeholderCategory]
                    },
                    "notes": {"type": ["string", "null"]}
                },
                "required": ["name", "stakeholder_category"]
            }
        },
        "relationships": {
            "type": "object",
            "properties": {
                "skipped": {"type": "boolean"},
                "skip_reason": {"type": ["string", "null"]},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "a_name": {"type": "string"},
                            "b_name": {"type": "string"},
                            "relationship": {"type": "string"},
                            "a_character": {"type": ["string", "null"], "enum": ["hero", "villain", "victim", None]},
                            "b_character": {"type": ["string", "null"], "enum": ["hero", "villain", "victim", None]},
                            "a_type": {"type": "string", "enum": [cat.value for cat in StakeholderCategory]},
                            "b_type": {"type": "string", "enum": [cat.value for cat in StakeholderCategory]},
                            "evidence": {"type": "string"}
                        },
                        "required": ["a_name", "b_name", "relationship", "a_character", "b_character", "a_type", "b_type", "evidence"]
                    }
                }
            },
            "required": ["skipped", "skip_reason", "items"]
        },
        "portrayals": {
            "type": "object",
            "properties": {
                "skipped": {"type": "boolean"},
                "skip_reason": {"type": ["string", "null"]},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "actor": {"type": "string"},
                            "type": {"type": "string", "enum": [t.value for t in PortrayalType]},
                            "evidence": {"type": "string"},
                            "explanation": {"type": "string"}
                        },
                        "required": ["actor", "type", "evidence", "explanation"]
                    }
                },
                "shifts": {
                    "type": "object",
                    "properties": {
                        "angel_shift_present": {"type": "boolean"},
                        "devil_shift_present": {"type": "boolean"},
                        "angel_shift_evidence": {"type": ["string", "null"]},
                        "devil_shift_evidence": {"type": ["string", "null"]}
                    },
                    "required": ["angel_shift_present", "devil_shift_present"]
                }
            },
            "required": ["skipped", "skip_reason", "items", "shifts"]
        },
        "issue_scope": {
            "type": "object",
            "properties": {
                "skipped": {"type": "boolean"},
                "skip_reason": {"type": ["string", "null"]},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "actor": {"type": "string"},
                            "type": {"type": "string", "enum": [t.value for t in IssueScopeType]},
                            "evidence": {"type": "string"},
                            "explanation": {"type": "string"}
                        },
                        "required": ["actor", "type", "evidence", "explanation"]
                    }
                }
            },
            "required": ["skipped", "skip_reason", "items"]
        },
        "causal_mechanisms": {
            "type": "object",
            "properties": {
                "skipped": {"type": "boolean"},
                "skip_reason": {"type": ["string", "null"]},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "actor": {"type": "string"},
                            "type": {"type": "string", "enum": [t.value for t in CausalMechanismType]},
                            "evidence": {"type": "string"},
                            "explanation": {"type": "string"}
                        },
                        "required": ["actor", "type", "evidence", "explanation"]
                    }
                }
            },
            "required": ["skipped", "skip_reason", "items"]
        },
        "ai_decisions": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "action": {"type": "string", "enum": ["add_actor", "remove_actor", "none"]},
                    "actor": {"type": ["string", "null"]},
                    "reasoning": {"type": "string"}
                },
                "required": ["action", "actor", "reasoning"]
            }
        }
    },
    "required": ["doc_id", "title", "actors", "relationships", "portrayals", "issue_scope", "causal_mechanisms", "ai_decisions"]
}