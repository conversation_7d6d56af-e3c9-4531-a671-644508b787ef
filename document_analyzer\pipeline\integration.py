"""
Data pipeline integration utilities for processing document batches.
"""

import json
import csv
from typing import List, Dict, Any, Optional, Iterator, Union
from pathlib import Path
from dataclasses import dataclass
from ..core.analyzer import DocumentAnalyzer, AnalysisRequest

@dataclass
class DocumentRecord:
    """Represents a document record from a data source."""
    doc_id: str
    title: Optional[str]
    text: str
    actors: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class DataPipelineIntegrator:
    """Handles integration with existing data pipelines."""
    
    def __init__(self):
        self.analyzer = DocumentAnalyzer()
    
    def load_from_csv(
        self, 
        filepath: Union[str, Path],
        doc_id_col: str = "doc_id",
        title_col: str = "title", 
        text_col: str = "text",
        actors_col: Optional[str] = "actors"
    ) -> Iterator[DocumentRecord]:
        """
        Load documents from a CSV file.
        
        Args:
            filepath: Path to CSV file
            doc_id_col: Column name for document ID
            title_col: Column name for document title
            text_col: Column name for document text
            actors_col: Optional column name for known actors
            
        Yields:
            DocumentRecord instances
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                actors = None
                if actors_col and row.get(actors_col):
                    # Handle different actor formats
                    actors_str = row[actors_col]
                    if actors_str.startswith('['):
                        # JSON array format
                        try:
                            actors = json.loads(actors_str)
                        except json.JSONDecodeError:
                            actors = [actors_str]
                    else:
                        # Comma-separated format
                        actors = [a.strip() for a in actors_str.split(',')]
                
                yield DocumentRecord(
                    doc_id=row[doc_id_col],
                    title=row.get(title_col),
                    text=row[text_col],
                    actors=actors,
                    metadata={k: v for k, v in row.items() if k not in [doc_id_col, title_col, text_col, actors_col]}
                )
    
    def load_from_json(self, filepath: Union[str, Path]) -> Iterator[DocumentRecord]:
        """
        Load documents from a JSON file.
        
        Args:
            filepath: Path to JSON file
            
        Yields:
            DocumentRecord instances
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            if isinstance(data, list):
                for doc in data:
                    yield DocumentRecord(
                        doc_id=doc.get('doc_id', str(hash(doc.get('text', '')))),
                        title=doc.get('title'),
                        text=doc['text'],
                        actors=doc.get('actors'),
                        metadata={k: v for k, v in doc.items() if k not in ['doc_id', 'title', 'text', 'actors']}
                    )
            else:
                yield DocumentRecord(
                    doc_id=data.get('doc_id', '000'),
                    title=data.get('title'),
                    text=data['text'],
                    actors=data.get('actors'),
                    metadata={k: v for k, v in data.items() if k not in ['doc_id', 'title', 'text', 'actors']}
                )
    
    def load_from_directory(
        self, 
        directory: Union[str, Path], 
        pattern: str = "*.txt"
    ) -> Iterator[DocumentRecord]:
        """
        Load documents from text files in a directory.
        
        Args:
            directory: Path to directory containing text files
            pattern: File pattern to match
            
        Yields:
            DocumentRecord instances
        """
        dir_path = Path(directory)
        for filepath in dir_path.glob(pattern):
            with open(filepath, 'r', encoding='utf-8') as f:
                text = f.read()
            
            # Use filename (without extension) as doc_id
            doc_id = filepath.stem
            
            yield DocumentRecord(
                doc_id=doc_id,
                title=None,
                text=text,
                actors=None,
                metadata={'source_file': str(filepath)}
            )
    
    def create_batch_prompts(
        self, 
        documents: Iterator[DocumentRecord],
        mode: str = "unified",
        output_file: Optional[Union[str, Path]] = None
    ) -> List[str]:
        """
        Create analysis prompts for a batch of documents.
        
        Args:
            documents: Iterator of DocumentRecord instances
            mode: Analysis mode
            output_file: Optional file to save prompts to
            
        Returns:
            List of formatted prompts
        """
        prompts = []
        
        for doc in documents:
            request = AnalysisRequest(
                doc_id=doc.doc_id,
                title=doc.title,
                text=doc.text,
                optional_known_actors=doc.actors
            )
            
            prompt = self.analyzer.analyze_document(request, mode)
            prompts.append(prompt)
        
        if output_file:
            self.save_prompts_to_file(prompts, output_file)
        
        return prompts
    
    def save_prompts_to_file(
        self, 
        prompts: List[str], 
        filepath: Union[str, Path]
    ) -> None:
        """Save prompts to a file."""
        with open(filepath, 'w', encoding='utf-8') as f:
            for i, prompt in enumerate(prompts):
                f.write(f"=== PROMPT {i+1} ===\n")
                f.write(prompt)
                f.write("\n\n")
    
    def process_analysis_results(
        self, 
        results: List[Dict[str, Any]],
        output_file: Optional[Union[str, Path]] = None
    ) -> List[Dict[str, Any]]:
        """
        Process and optionally save analysis results.
        
        Args:
            results: List of analysis result dictionaries
            output_file: Optional file to save results to
            
        Returns:
            Processed results
        """
        processed_results = []
        
        for result in results:
            # Add processing timestamp and validation status
            processed_result = {
                **result,
                "processed_at": json.dumps(None),  # Could add timestamp
                "validation_status": "validated" if self.analyzer.validator.validate(result)[0] else "invalid"
            }
            processed_results.append(processed_result)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_results, f, indent=2, ensure_ascii=False)
        
        return processed_results
    
    def create_actor_consistency_mapping(
        self, 
        results: List[Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Create a mapping of actors across documents for consistency checking.
        
        Args:
            results: List of analysis results
            
        Returns:
            Dictionary mapping actor names to their attributes across documents
        """
        actor_mapping = {}
        
        for result in results:
            doc_id = result.get("doc_id")
            for actor in result.get("actors", []):
                actor_name = actor["name"]
                if actor_name not in actor_mapping:
                    actor_mapping[actor_name] = {
                        "stakeholder_categories": set(),
                        "documents": [],
                        "notes": []
                    }
                
                actor_mapping[actor_name]["stakeholder_categories"].add(actor["stakeholder_category"])
                actor_mapping[actor_name]["documents"].append(doc_id)
                if actor.get("notes"):
                    actor_mapping[actor_name]["notes"].append(actor["notes"])
        
        # Convert sets to lists for JSON serialization
        for actor_name, data in actor_mapping.items():
            data["stakeholder_categories"] = list(data["stakeholder_categories"])
        
        return actor_mapping