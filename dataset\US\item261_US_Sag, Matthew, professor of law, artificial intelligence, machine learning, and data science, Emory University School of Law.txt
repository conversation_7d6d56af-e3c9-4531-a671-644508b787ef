  STATEMENT OF DANA RAO, EXECUTIVE VICE PRESIDENT, GENERAL 
   COUNSEL, AND CHIEF TRUST OFFICER, ADOBE, INC., SAN JOSE, 
                           CALIFORNIA

    Mr. <PERSON>. Chair <PERSON><PERSON>, Ranking Member <PERSON><PERSON>, and Members of 
the Committee, thank you for the opportunity to testify here 
today.
    My name is <PERSON>, and I am general counsel, and, as 
<PERSON> <PERSON><PERSON> noted, chief trust officer at Adobe. I am happy 
to provide you with this secret certificate you need to get 
that title, if you would like, after the hearing.
    Since our founding in 1982, Adobe has pioneered 
transformative technologies in all types of digital creation, 
from digital documents like PDF to image editing with 
Photoshop. Our products allow our customers who range from 
aspiring artists to wartime photojournalists, to advertisers 
and more, to unleash their creativity, protect their craft, 
empower their businesses in a digital world.
    AI is the latest disruptive technology we have been 
incorporating into our tools help creators realize their 
potential. You have all seen the magic of text to image 
generative AI. Type in the prompt, cat driving a 1950s 
sportscar through the desert, and in seconds you will see 
multiple variations of a cat on a retro road trip appear before 
your eyes.
    We have launched generative AI in our own tools, Adobe 
Firefly, and has provided--this proved to be wildly popular 
with our creative professionals and consumers alike. In my 
written testimony, I explore a comprehensive framework for 
responsible AI development that includes addressing 
misinformation, harmful bias, creative rights, and intellectual 
property.
    Today, given Adobe's focus and our millions of creative 
customers and our leadership in AI, I will focus on how the 
United States can continue to lead the world in AI development 
by both supporting the access to data that AI requires and 
strengthening creator rights.
    The question of data access is critical for the development 
of AI because AI is only as powerful and as good as the data on 
which it is trained. Like the human brain, AI learns from the 
information you give it.
    In the AI's case, the data it is trained on. Training on a 
larger dataset can help ensure your results are more accurate 
because the AI has more facts to learn from. A larger dataset 
will also help the AI avoid perpetuating harmful biases in its 
results by giving it a wider breadth of experiences from which 
it can build its understanding of the world. More data means 
better answers and fewer biases.
    Given those technical realities, United States and 
governments should support access to data to ensure that AI 
innovation can flourish accurately and responsibly. However, 
one of the most important implications of AI's need for data is 
the impact on copyright and creators' rights.
    There are many outstanding questions in this space, 
including whether creating an AI model, which is a software 
program, from a set of images, is a permitted fair use. And 
whether that analysis changes if the output of that AI model 
creates an image that is substantially similar to an image on 
which it is trained.
    These questions will certainly be addressed by courts and 
perhaps Congress, and we are prepared to help assist in those 
discussions. Adobe recognized the potential impact of AI on 
creators and society, and we have taken several steps.
    First, we trained our own generative AI tool, Adobe 
Firefly, only on licensed images from our Adobe Stock 
Collection, which is a stock photography collection, openly 
licensed content, and works that are in the public domain where 
the copyright has expired. This approach supports creators and 
customers by training on a dataset that is designed to be 
commercially safe.
    In addition, we are advocating for other steps we can all 
take to strengthen creators' rights. First, we believe creators 
should be able to attach a ``Do Not Train'' tag to their work. 
With industry and Government support, we can ensure AI data 
crawlers will read and respect this tag, giving creators the 
option to keep their data out of AI training datasets.
    Second, creators using AI tools want to ensure they can 
obtain copyright protection over their work in this new era of 
AI-assisted digital creation. An AI output alone may not 
receive copyright protection, but we believe the combination of 
human expression and AI expression will and should.
    Content editing tools should enable creators to obtain a 
copyright by allowing them to distinguish the AI work from the 
human work. In my written testimony, I discuss our open 
standards-based technology content credentials, which can help 
enable both of these creator protections.
    Finally, even though Adobe has trained its AI on permitted 
work, we understand the concern that an artist can be 
economically dispossessed by an AI trained on their work that 
generates arts in their style, in the Frank Sinatra example you 
gave.
    We believe artists should be protected against this type of 
economic harm, and we propose Congress establish a new Federal 
anti-impersonation right that would give artists a right to 
enforce against someone intentionally attempting to impersonate 
their style or likeness.
    Holding people accountable who misuse AI tools is a 
solution we believe goes to the heart of some of the issues our 
customers have, and this new right would help address that 
concern. The United States has led the world through 
technological transformations in the past, and we have all 
learned it is important to be proactively responsible to the 
impact of these technologies.
    Pairing innovation with responsible innovation will ensure 
that AI ultimately becomes a transformative and true benefit to 
our society. Thank you, Chair Coons, Ranking Member Tillis, and 
Members of the Committee.
    [The prepared statement of Mr. Rao appears as a submission 
for the record.]
    Chair Coons. Thank you, Mr. Rao. Professor.

    STATEMENT OF MATTHEW SAG, PROFESSOR OF LAW, ARTIFICIAL 
    INTELLIGENCE, MACHINE LEARNING, AND DATA SCIENCE, EMORY 
           UNIVERSITY SCHOOL OF LAW, ATLANTA, GEORGIA

    Professor Sag. Chair Coons, Ranking Member Tillis, Members 
of the Subcommittee, thank you for the opportunity to testify 
here today. I am a professor of law in AI, machine learning, 
and data science at Emory University, where I was hired as part 
of Emory's AI Humanity Initiative.
    Although we are still a long way from the science fiction 
version of artificial general intelligence that thinks, feels, 
and refuses to open the pod bay doors, recent advances in 
machine learning and artificial intelligence have captured the 
public's attention and apparently lawmakers' interest.
    We now have large language models, or LLMs, that can pass 
the bar exam, carry on a conversation, create new music and new 
visual art. Nonetheless, copyright law does not and should not 
recognize computer systems as authors. Even where an AI 
produces images, text, or music that is indistinguishable from 
human authored works, it makes no sense to think of a machine 
learning program as the author.
    The Copyright Act rightly reserves copyrights for original 
works of authorship. As the Supreme Court explained long ago in 
the 1884 case of Burrow-Giles Lithographic, authorship entails 
original, intellectual conception. An AI can't produce a work 
that reflects its own original intellectual conception because 
it has none.
    Thus, when AI models produce content with little or no 
human oversight, there is no copyright in those outputs. 
However, humans using AI as tools of expression may claim 
authorship if the final form of the work reflects their 
original intellectual conception in sufficient detail. And I 
have elaborated in my written submissions how this will depend 
on the circumstances.
    Training generative AI on copyrighted works is usually fair 
use because it falls into the category of non-expressive use. 
Courts addressing technologies such as reverse engineering, 
search engines, and plagiarism detection software have held 
that these non-expressive uses are fair use. These cases 
reflect copyright's fundamental distinction between protectable 
original expression and unprotect-able facts, ideas, and 
abstractions.
    Whether training an LLM is in non-expressive use depends 
ultimately on the outputs of the model. If an LLM is trained 
properly and operated with appropriate safeguards, its outputs 
will not resemble its inputs in a way that would trigger a 
copyright liability. Training such an LLM on copyrighted works 
would thus be justified under current fair use principles.
    It is important to understand that generative AI are not 
designed to copy original expression. One of the most common 
misconceptions about generative AI is the notion that the 
training data is somehow copied into the model. Machine 
learning models are influenced by the data. They would be 
pretty useless without it. But they typically don't copy the 
data in any literal sense.
    So rather than thinking of an LLM as copying the training 
data like a scribe in a monastery, it makes more sense to think 
of it as learning from the training data like a student. If an 
LLM like GPT3 is working as intended, it doesn't copy the 
training data at all. The only copying that takes place is when 
the training corpus is assembled and pre-processed, and that is 
what you need a fair use justification for. Whether a 
generative AI produces truly new content or simply conjures up 
an infringing cut and paste of works in the training data 
depends on how it is trained.
    Accordingly, companies should adopt best practices to 
reduce the risk of copyright infringement and other related 
harms, and I have elaborated on some of these best practices in 
my written submission. Failure to adopt best practices may 
potentially undermine claims of fair use.
    Generative AI does not, in my opinion, require a major 
overhaul of the U.S. copyright system at this time.
    If Congress is considering new legislation in relation to 
AI and copyright, that legislation should be targeted at 
clarifying the application of existing fair use jurisprudence, 
not overhauling it.
    Israel, Singapore, and South Korea have recently 
incorporated fair use into their copyright statutes because 
these countries recognize that the flexibility of the fair use 
doctrine gives U.S. companies and U.S. researchers a 
significant competitive advantage.
    Several other jurisdictions, most notably Japan, the United 
Kingdom, and the European Union, have specifically adopted 
exemptions for text data mining that allow use of copyrighted 
works as training for machine learning and other purposes.
    Copyright law should encourage the developers of generative 
AI to act responsibly. However, if our laws become overly 
restrictive, then corporations and researchers will simply move 
key aspects of technology development overseas to our 
competitors.
    Thank you very much.
    [The prepared statement of Professor Sag appears as a 
submission for the record.]
    Chair Coons. Thank you, Professor. Ms. Ortiz.

      STATEMENT OF KARLA ORTIZ, CONCEPT ARTIST, ILLUSTRA-
        TOR, AND FINE ARTIST, SAN FRANCISCO, CALIFORNIA

    Ms. Ortiz. Yes. Chairman Coons, Ranking Member Tillis, and 
esteemed Members of the Committee, it is an honor to testify 
before you today about AI and copyright. My name is Karla 
Ortiz. I am a concept artist, illustrator, and fine artist, and 
you may not know my name, but you know my work.
    My paintings have shaped the worlds of blockbuster Marvel 
films and TV shows, including ``Guardians of the Galaxy 3,'' 
``Black Panther,'' ``Loki,'' you know, but specifically, the 
one I am most happiest of is that I, my work helped shape the 
look of Doctor Strange in the first ``Doctor Strange'' movie.
    I have to brag about that a little bit, sir. I love what I 
do. I love my craft. Artists train their entire lives to be 
able to bring the imaginary to life. All of us who engage in 
this craft love every little bit of it. Through hard work, 
support of loved ones, and dedication, I have been able to make 
a good living from my craft via the entertainment industry, an 
industry that thrives when artists' rights to consent, credit, 
and compensation are respected.
    I have never worried about my future as an artist until 
now. Generative AI is unlike any other technology that has come 
before. It is a technology that uniquely consumes and exploits 
the hard work, creativity, and innovation of others. No other 
tool is like this. What I found, when first researching AI, 
horrified me.
    I found that almost the entirety of my work, the work of 
almost every artist I know, and the work of hundreds of 
thousands of artists had been taken without our consent, 
credit, or compensation. These works were stolen and used to 
train for profit technologies with datasets that contain 
billions of image and text data pairs.
    Through my research, I learned many AI companies gather 
copyrighted training data by relying on a practice called data 
laundering. This is where a company outsources its data 
collection to a third party under the pretext of research to 
then immediately use commercially. I found these companies use 
big terms like ``publicly available data'' or ``openly licensed 
content'' to disguise their extensive reliance on copyrighted 
works.
    No matter what they are saying, these models are illegally 
trained on copyrighted works. To add even more insult to 
injury, I found that these for-profit companies were not only 
permitting users to use our full names to generate imagery but 
encouraging it. For example, Polish artist Frederic Koski had 
had his name used as a prompt in AI products over 400,000 
times, and those are the lower end of the estimate.
    My own name, Karla Ortiz, has also been used by these 
companies thousands of times. Never once did I give consent. 
Never once have I gotten credit. Never once have I gotten 
compensation. It should come as no surprise that major 
productions are replacing artists with generative AI.
    Goldman Sachs estimates that generative AI will diminish or 
outright destroy approximately 300 million full-time jobs 
worldwide. As Ranking Member Tillis mentioned earlier, 
copyright-reliant industries alone contribute $1.8 trillion 
value to the U.S. GDP, accounting for 7.76 percent of the 
entire U.S. economy. This is an industry that employs 9.6 
million American workers alone.
    The game plan is simple, to go as fast as possible, to 
create mesmerizing tales of progress, and to normalize the 
exploitation of artists as quickly as possible. They hope when 
we catch our breath, it will be too late to right the wrongs, 
and exploiting Americans will become an accepted way of doing 
things.
    But that game can't succeed as we are here now, giving this 
the urgency it so desperately deserves. Congress should act to 
ensure what we call the 3Cs and a T: consent, credit, 
compensation, and transparency.
    The work of artists like myself were taken without our 
consent, credit, nor compensation, and then used to compete 
with us directly in our own markets--an outrageous act that 
under any other context would immediately be seen as unfair, 
immoral, and illegal.
    Senators, there is a fundamental fairness issue here. I am 
asking Congress to address this by enacting laws that require 
these companies to obtain consent, give credit, pay 
compensation, and be transparent. Thank you.