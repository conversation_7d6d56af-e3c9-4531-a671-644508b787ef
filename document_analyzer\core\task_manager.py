"""
Task manager and orchestrator for running document analysis workflows.
"""

import json
import asyncio
from typing import List, Dict, Any, Optional, Callable, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from pathlib import Path
import logging
from datetime import datetime

from ..core.analyzer import DocumentAnalyzer, AnalysisRequest
from ..pipeline.integration import DataPipelineIntegrator, DocumentRecord

@dataclass
class AnalysisJob:
    """Represents a single analysis job."""
    job_id: str
    request: AnalysisRequest
    mode: str
    status: str = "pending"  # pending, running, completed, failed
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

@dataclass
class BatchAnalysisConfig:
    """Configuration for batch analysis."""
    mode: str = "unified"
    max_workers: int = 4
    retry_attempts: int = 2
    output_directory: Optional[str] = None
    save_prompts: bool = True
    save_results: bool = True
    validate_results: bool = True

class AnalysisTaskManager:
    """Manages and orchestrates document analysis tasks."""
    
    def __init__(self, config: Optional[BatchAnalysisConfig] = None):
        self.analyzer = DocumentAnalyzer()
        self.pipeline = DataPipelineIntegrator()
        self.config = config or BatchAnalysisConfig()
        self.jobs: Dict[str, AnalysisJob] = {}
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for the task manager."""
        logger = logging.getLogger("document_analyzer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def create_job(
        self, 
        request: AnalysisRequest, 
        mode: str = "unified"
    ) -> str:
        """
        Create a new analysis job.
        
        Args:
            request: Analysis request
            mode: Analysis mode
            
        Returns:
            Job ID
        """
        job_id = f"{request.doc_id}_{mode}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        job = AnalysisJob(
            job_id=job_id,
            request=request,
            mode=mode,
            created_at=datetime.now()
        )
        self.jobs[job_id] = job
        return job_id
    
    def get_job_status(self, job_id: str) -> Optional[AnalysisJob]:
        """Get the status of a specific job."""
        return self.jobs.get(job_id)
    
    def list_jobs(self, status_filter: Optional[str] = None) -> List[AnalysisJob]:
        """List all jobs, optionally filtered by status."""
        jobs = list(self.jobs.values())
        if status_filter:
            jobs = [job for job in jobs if job.status == status_filter]
        return jobs
    
    def execute_single_job(self, job_id: str) -> bool:
        """
        Execute a single analysis job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if successful, False otherwise
        """
        job = self.jobs.get(job_id)
        if not job:
            self.logger.error(f"Job {job_id} not found")
            return False
        
        job.status = "running"
        self.logger.info(f"Starting job {job_id}")
        
        try:
            # Generate prompt
            prompt = self.analyzer.analyze_document(job.request, job.mode)
            
            # In a real implementation, this would call an LLM API
            # For now, we'll simulate by creating a structured response
            result = self._simulate_llm_response(job.request, job.mode)
            
            # Validate result if configured
            if self.config.validate_results:
                is_valid, error, _ = self.analyzer.validate_analysis_result(
                    json.dumps(result), job.request.text
                )
                if not is_valid:
                    job.status = "failed"
                    job.error = f"Validation failed: {error}"
                    return False
            
            job.result = result
            job.status = "completed"
            job.completed_at = datetime.now()
            
            self.logger.info(f"Completed job {job_id}")
            return True
            
        except Exception as e:
            job.status = "failed"
            job.error = str(e)
            job.completed_at = datetime.now()
            self.logger.error(f"Job {job_id} failed: {str(e)}")
            return False
    
    def _simulate_llm_response(self, request: AnalysisRequest, mode: str) -> Dict[str, Any]:
        """Simulate an LLM response for testing purposes."""
        # This is a placeholder - in real usage, this would call an actual LLM
        return {
            "doc_id": request.doc_id,
            "title": request.title,
            "actors": [],
            "relationships": {"skipped": True, "skip_reason": "Simulated response", "items": []},
            "portrayals": {"skipped": True, "skip_reason": "Simulated response", "items": [], "shifts": {"angel_shift_present": False, "devil_shift_present": False}},
            "issue_scope": {"skipped": True, "skip_reason": "Simulated response", "items": []},
            "causal_mechanisms": {"skipped": True, "skip_reason": "Simulated response", "items": []},
            "ai_decisions": [{"action": "none", "actor": None, "reasoning": "Simulated response"}]
        }
    
    def execute_batch_jobs(
        self, 
        job_ids: List[str],
        max_workers: Optional[int] = None
    ) -> Dict[str, bool]:
        """
        Execute multiple jobs in parallel.
        
        Args:
            job_ids: List of job IDs to execute
            max_workers: Maximum number of worker threads
            
        Returns:
            Dictionary mapping job IDs to success status
        """
        max_workers = max_workers or self.config.max_workers
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all jobs
            future_to_job = {
                executor.submit(self.execute_single_job, job_id): job_id 
                for job_id in job_ids
            }
            
            # Collect results
            for future in as_completed(future_to_job):
                job_id = future_to_job[future]
                try:
                    success = future.result()
                    results[job_id] = success
                except Exception as e:
                    self.logger.error(f"Job {job_id} raised exception: {str(e)}")
                    results[job_id] = False
        
        return results
    
    def process_document_batch(
        self, 
        documents: List[DocumentRecord],
        mode: str = "unified"
    ) -> List[str]:
        """
        Process a batch of documents and return job IDs.
        
        Args:
            documents: List of document records
            mode: Analysis mode
            
        Returns:
            List of created job IDs
        """
        job_ids = []
        
        for doc in documents:
            request = AnalysisRequest(
                doc_id=doc.doc_id,
                title=doc.title,
                text=doc.text,
                optional_known_actors=doc.actors
            )
            job_id = self.create_job(request, mode)
            job_ids.append(job_id)
        
        self.logger.info(f"Created {len(job_ids)} jobs for batch processing")
        return job_ids
    
    def process_from_directory(
        self, 
        directory: Union[str, Path],
        mode: str = "unified",
        pattern: str = "*.txt",
        execute: bool = True
    ) -> List[str]:
        """
        Process all documents from a directory.
        
        Args:
            directory: Directory containing documents
            mode: Analysis mode
            pattern: File pattern to match
            execute: Whether to execute jobs immediately
            
        Returns:
            List of job IDs
        """
        documents = list(self.pipeline.load_from_directory(directory, pattern))
        job_ids = self.process_document_batch(documents, mode)
        
        if execute:
            self.logger.info(f"Executing {len(job_ids)} jobs...")
            results = self.execute_batch_jobs(job_ids)
            successful = sum(1 for success in results.values() if success)
            self.logger.info(f"Completed {successful}/{len(job_ids)} jobs successfully")
        
        return job_ids
    
    def export_results(
        self, 
        output_file: Union[str, Path],
        status_filter: Optional[str] = "completed"
    ) -> None:
        """
        Export job results to a file.
        
        Args:
            output_file: Output file path
            status_filter: Filter jobs by status
        """
        jobs = self.list_jobs(status_filter)
        results = []
        
        for job in jobs:
            if job.result:
                result_data = {
                    "job_id": job.job_id,
                    "doc_id": job.request.doc_id,
                    "mode": job.mode,
                    "status": job.status,
                    "created_at": job.created_at.isoformat() if job.created_at else None,
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                    "result": job.result
                }
                results.append(result_data)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Exported {len(results)} results to {output_file}")
    
    def get_job_statistics(self) -> Dict[str, Any]:
        """Get statistics about all jobs."""
        jobs = list(self.jobs.values())
        total = len(jobs)
        
        if total == 0:
            return {"total": 0, "by_status": {}, "by_mode": {}}
        
        by_status = {}
        by_mode = {}
        
        for job in jobs:
            # Count by status
            by_status[job.status] = by_status.get(job.status, 0) + 1
            
            # Count by mode
            by_mode[job.mode] = by_mode.get(job.mode, 0) + 1
        
        return {
            "total": total,
            "by_status": by_status,
            "by_mode": by_mode,
            "success_rate": by_status.get("completed", 0) / total if total > 0 else 0
        }

class AnalysisWorkflow:
    """High-level workflow orchestrator for common analysis patterns."""
    
    def __init__(self):
        self.task_manager = AnalysisTaskManager()
    
    def full_document_analysis(
        self, 
        documents: List[DocumentRecord],
        output_dir: Optional[str] = None
    ) -> Dict[str, List[str]]:
        """
        Run full analysis on documents using all analysis modes.
        
        Args:
            documents: List of document records
            output_dir: Optional output directory
            
        Returns:
            Dictionary mapping modes to job IDs
        """
        modes = ["unified", "entities", "portrayals", "issues", "causal"]
        all_job_ids = {}
        
        for mode in modes:
            job_ids = self.task_manager.process_document_batch(documents, mode)
            all_job_ids[mode] = job_ids
        
        # Execute all jobs
        for mode, job_ids in all_job_ids.items():
            self.task_manager.logger.info(f"Executing {mode} analysis jobs...")
            self.task_manager.execute_batch_jobs(job_ids)
        
        # Export results if output directory specified
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            for mode in modes:
                output_file = output_path / f"{mode}_results.json"
                # Filter jobs by mode for export
                mode_jobs = [job for job in self.task_manager.list_jobs("completed") if job.mode == mode]
                
                results = []
                for job in mode_jobs:
                    if job.result:
                        results.append({
                            "job_id": job.job_id,
                            "doc_id": job.request.doc_id,
                            "result": job.result
                        })
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
        
        return all_job_ids
    
    def comparative_analysis(
        self, 
        documents: List[DocumentRecord],
        modes: List[str] = ["entities", "portrayals"]
    ) -> Dict[str, Any]:
        """
        Run comparative analysis using specified modes.
        
        Args:
            documents: List of document records
            modes: Analysis modes to compare
            
        Returns:
            Comparison results
        """
        job_results = {}
        
        for mode in modes:
            job_ids = self.task_manager.process_document_batch(documents, mode)
            self.task_manager.execute_batch_jobs(job_ids)
            
            # Collect results
            mode_results = []
            for job_id in job_ids:
                job = self.task_manager.get_job_status(job_id)
                if job and job.result:
                    mode_results.append(job.result)
            
            job_results[mode] = mode_results
        
        return job_results