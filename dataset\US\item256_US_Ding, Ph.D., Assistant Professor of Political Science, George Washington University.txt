  STATEMENT OF JEFFREY DING, PhD, ASSISTANT PROFESSOR OF 
        POLITICAL SCIENCE, <PERSON>ORGE WASHINGTON UNIVERSITY

    Dr. Ding. Chairman <PERSON>, Vice Chairman <PERSON><PERSON><PERSON>, and Members 
of the Committee. I am honored by the opportunity to brief this 
Committee on the National Security Implications of AI. In all 
honesty, I also have a selfish reason for attending today. I 
teach political science at GW, and my students all really look 
up to the Committee Members in this room and also all the staff 
who are working behind the scenes to put this hearing together. 
So, when I got to tell the class this morning that I was doing 
this testimony, they all got the most excited I've ever seen 
them get excited this semester. And so, hopefully, that will 
cause them to do more of the required readings in class. In all 
seriousness, I have great students, I'm very grateful to be 
here.
    Today, in my opening remarks, I want to make three main 
points from my written testimony. The first is when it comes to 
the national security of implications of AI, the main driver 
and the main vector is which country will be able to sustain 
productivity growth at higher levels than their rivals. And for 
this vector, the distinction between innovation capacity and 
diffusion capacity is central to thinking about technological 
leadership in AI. Today, when various groups--whether that be 
experts, policymakers, the Intelligence Community--when they 
try to assess technological leadership, they are overly 
preoccupied with innovation capacity. Which state is going to 
be the first to generate new-to-the-world breakthroughs, the 
first to generate that next leap in large language models. They 
neglect diffusion capacity. A state's ability to spread and 
adopt innovations after their initial introduction across 
productive processes.
    And that process of diffusion throughout the entire economy 
is really important for technologies like AI. If we were 
talking about a sector like automobiles, or even a sector like 
clean energy, we might not be talking as much about the effects 
of spreading technologies across all different productive 
processes throughout the entire economy. AI is a general-
purpose technology, like electricity, like the computer, like 
my fellow panelists just mentioned in his testimony. And 
general-purpose technologies historically precede waves of 
productivity growth because they can have pervasive effects 
throughout the entire economy. So, the U.S. in the late 19th 
century became the leading economic power before it translated 
that influence into military and geopolitical leadership, 
because it was better at adopting general purpose technologies 
at scale, like electricity, like the American system of 
interchangeable manufacture, at a better and a more effective 
rate than its rivals.
    Point number two is when we assess China's technological 
leadership and use this framework of innovation capacity versus 
diffusion capacity, my research finds that China faces a 
diffusion deficit. Its ability to diffuse innovations like AI 
across the entire economy lags far behind its ability to 
pioneer initial innovations or make fundamental breakthroughs.
    And so, when you've heard from other people in the past or 
in the briefing memos you are reading, you are probably getting 
a lot of innovation-centric indicators of China's scientific 
and technological prowess: its advances in R&D spending, 
headline numbers on patents and publications. In my research, 
I've presented evidence about China's diffusion deficit by 
looking at how is China actually adopting other information and 
communications technologies at scale? What are its adoption 
rates in cloud computing, industrial software, related 
technologies that would all be in a similar category to AI? And 
those rates lag far behind the U.S.
    Another indicator would be how is China's ability to widen 
the pool of average AI engineers? I'm not talking about Nobel 
Prize of computing winners like my fellow panelists here, but 
just average AI engineers who can take existing models and 
adapt them in particular sectors or industries or specific 
applications. And based on my data, China has only 29 
universities that meet a baseline quality metric for AI 
engineering, whereas the U.S. has 159. So, there's a large gap 
in terms of China's diffusion capacity compared to its 
innovation capacity in AI.
    I'll close with the third point, which is some recent 
trends in Chinese labs' large language models. China has built 
large language models similar to OpenAI's ChatGPT, as well as 
OpenAI's text-to-image models like DALL-E. But there's still a 
large gap in terms of Chinese performance on these models. And, 
in fact, on benchmarks and leaderboards where U.S. models are 
compared to Chinese models on Chinese language prompts, models 
like ChatGPT still perform better than Chinese counterparts. 
Some of these bottlenecks relate to a reliance on Western 
companies to open up new paradigms, China's censorship regime, 
which Dr. Jensen talked about, and computing power bottlenecks, 
which I'm happy to expand on further.
    I'll close by saying I submitted three specific policy 
recommendations to the Committee. But I want to emphasize one, 
which is keep calm and avoid overhyping China's AI 
capabilities. In the paper that forms the basis for this 
testimony, I called attention to a 1969 CIA assessment of the 
Soviet Union's technological capabilities. It was remarkable 
because it went against the dominant narrative of the time of a 
Soviet Union close to overtaking the U.S. in technological 
leadership. The report concluded that the technological gap was 
actually widening between the U.S. as the leader and the Soviet 
Union because of the U.S.'s superior mechanisms to spread 
technologies and diffuse technologies. Fifty years later, we 
know why this assessment was right, and we know we have to 
focus on diffusion capacity when it comes to scientific and 
technological leadership.
    Thanks for your time.