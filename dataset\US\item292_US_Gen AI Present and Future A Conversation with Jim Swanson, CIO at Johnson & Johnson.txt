Gen AI Present and Future: A Conversation with <PERSON>, CIO at Johnson & Johnson

With all these AI programs underway, you must have security concerns. What protocols is J&J putting in place to combat the growing risks associated with these types of threats?
As an organization, we watch out for all types of risks, which is why we spend so much time on governance. We don’t view this as an afterthought – it is part of how we think and embedded up front in our approach.

To defend against threats, we’ve created secure enclaves within the company for our large and small language models, and use vetted, curated data. Additionally, we created policies that educate our employees on the risks and restrictions around use of public Gen AI and direct them towards use of our internal generative AI application. We also review every proposed Gen AI use case through a process that includes security and architecture experts.

Thinking broadly, how else would you like to see AI or Gen AI technologies deployed in the coming years?
As the use of AI models increases, organizations will face greater challenges in the areas of data movement, costs, copyright and intellectual property. Therefore, within our company, I would most like to see improved observability to help mitigate threats in these areas.

There are also evolutions coming surrounding the types of data being used within these models. Today, we don’t create our own large language models, but maybe down the road, if it gets really cost effective, we could. We do use small language models as well, and for both types, there is a heavy focus on prompt engineering and data curation to improve the efficacy of these models. I expect these capabilities to also evolve over time.

For us, it all comes down to outcomes. How can this technology benefit the patients and healthcare providers we serve around the world? When we pair amazing science with amazing technology, we can truly reimagine healthcare.