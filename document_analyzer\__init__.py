"""
Document Analysis Framework
A comprehensive system for analyzing documents to extract actors, relationships, 
portrayals, issue scope strategies, and causal mechanisms.
"""

__version__ = "1.0.0"
__author__ = "Document Analysis Framework"

from .core.analyzer import DocumentAnalyzer
from .core.schema import AnalysisResult, ActorInfo, RelationshipInfo, PortrayalInfo, IssueScopeInfo, CausalMechanismInfo
from .utils.validator import JSONValidator

__all__ = [
    "DocumentAnalyzer",
    "AnalysisResult", 
    "ActorInfo",
    "RelationshipInfo", 
    "PortrayalInfo",
    "IssueScopeInfo",
    "CausalMechanismInfo",
    "JSONValidator"
]