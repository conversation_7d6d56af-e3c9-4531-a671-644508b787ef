"""
JSON validation utilities for document analysis results.
"""

import json
import jsonschema
from typing import Dict, Any, List, Optional
from ..core.schema import ANALYSIS_SCHEMA

class JSONValidator:
    """Validates JSON output against the analysis schema."""
    
    def __init__(self):
        self.schema = ANALYSIS_SCHEMA
    
    def validate(self, data: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        Validate JSON data against the schema.
        
        Args:
            data: Dictionary to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            jsonschema.validate(data, self.schema)
            return True, None
        except jsonschema.ValidationError as e:
            return False, str(e)
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def validate_json_string(self, json_string: str) -> tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Parse and validate a JSON string.
        
        Args:
            json_string: JSON string to validate
            
        Returns:
            Tuple of (is_valid, error_message, parsed_data)
        """
        try:
            data = json.loads(json_string)
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {str(e)}", None
        
        is_valid, error = self.validate(data)
        return is_valid, error, data if is_valid else None
    
    def check_actor_references(self, data: Dict[str, Any]) -> List[str]:
        """
        Check that all actor references in analysis sections exist in the actors array.
        
        Args:
            data: Validated analysis data
            
        Returns:
            List of missing actor references
        """
        actor_names = {actor["name"] for actor in data.get("actors", [])}
        missing_actors = []
        
        # Check relationships
        for rel in data.get("relationships", {}).get("items", []):
            if rel["a_name"] not in actor_names:
                missing_actors.append(f"relationships.a_name: {rel['a_name']}")
            if rel["b_name"] not in actor_names:
                missing_actors.append(f"relationships.b_name: {rel['b_name']}")
        
        # Check portrayals
        for port in data.get("portrayals", {}).get("items", []):
            if port["actor"] not in actor_names:
                missing_actors.append(f"portrayals.actor: {port['actor']}")
        
        # Check issue scope
        for issue in data.get("issue_scope", {}).get("items", []):
            if issue["actor"] not in actor_names:
                missing_actors.append(f"issue_scope.actor: {issue['actor']}")
        
        # Check causal mechanisms
        for causal in data.get("causal_mechanisms", {}).get("items", []):
            if causal["actor"] not in actor_names:
                missing_actors.append(f"causal_mechanisms.actor: {causal['actor']}")
        
        return list(set(missing_actors))
    
    def validate_evidence_quotes(self, data: Dict[str, Any], document_text: str) -> List[str]:
        """
        Check that evidence quotes exist in the document text.
        
        Args:
            data: Validated analysis data
            document_text: Original document text
            
        Returns:
            List of evidence quotes not found in document
        """
        missing_evidence = []
        
        # Check relationships
        for rel in data.get("relationships", {}).get("items", []):
            if rel["evidence"] not in document_text:
                missing_evidence.append(f"relationships evidence: {rel['evidence'][:50]}...")
        
        # Check portrayals
        for port in data.get("portrayals", {}).get("items", []):
            if port["evidence"] not in document_text:
                missing_evidence.append(f"portrayals evidence: {port['evidence'][:50]}...")
        
        # Check issue scope
        for issue in data.get("issue_scope", {}).get("items", []):
            if issue["evidence"] not in document_text:
                missing_evidence.append(f"issue_scope evidence: {issue['evidence'][:50]}...")
        
        # Check causal mechanisms
        for causal in data.get("causal_mechanisms", {}).get("items", []):
            if causal["evidence"] not in document_text:
                missing_evidence.append(f"causal_mechanisms evidence: {causal['evidence'][:50]}...")
        
        return missing_evidence