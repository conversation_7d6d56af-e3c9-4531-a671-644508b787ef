Authenticating AI-Generated Content

Policy / Best Practice Recommendations
Authenticating AI content is a newly emerging field — ripe for innovation and the
development and adoption of best practices. In line with our 2021 Global AI Policy
Recommendations38 and Policy Guide for Understanding Foundation Models & the
AI Value Chain published earlier this year,39 ITI recommends that as governments
consider AI regulation, they first scope and understand specific harms, keeping the
impact on innovation top of mind. As policymakers contemplate the development of
regulation and guidelines regarding AI-generated content and authentication, we
offer the following considerations:
Enable and allow innovation in AI
authentication to grow and thrive.
Policymakers should be careful not to be overly prescriptive in establishing regulation in this area
because state-of-the-art technology for verification and authentication of AI content is still being
developed and studied, and some authentication systems are only beginning to be deployed at scale.
While AI authentication techniques offer promising solutions to verify and assure content, more research
and investment are needed in this area before a definitive recommendation can be made as to the
type of authentication that should take place. It may be the case that multiple types of authentication
are appropriate for a given output. For example, the C2PA Coalition supports both watermarking and
content credentials to include content provenance to engender public trust in digital content in the age
of ubiquitous AI. Any approach to oversight or control in this domain has the potential to become quickly
outdated, especially as authentication techniques will continue to evolve alongside the technology itself.
Promote consumer transparency and awareness
around AI-generated content.
While industry is in the process of developing solutions for AI authentication, no one technical fix will
be a silver bullet. Consistent with our AI Transparency Policy Principles,40 we believe that consumers
should understand when a piece of content is AI-generated, and also be familiar with the tools provided
to them to do so. More broadly, users should be informed of the capabilities of the AI system, including
what it can and cannot do. Governments should likewise support guidance and programs to consumers
to spread consumer literacy and boost the public’s ability to determine when content is AI-generated.
4
3
5
Recognize that AI authentication is a shared responsibility.
Leverage public-private partnerships to understand both the
opportunities and limitations of various authentication techniques.
Foster interoperability and collaboration
across AI authentication techniques.
Collaboration across industry and with other stakeholders like government bodies and academic
researchers will be key to success in this area. Government-backed studies and stakeholder
engagement via multistakeholder processes or working groups in collaboration with affected
communities and the technical ecosystem is a helpful approach to advancing these techniques.
Public-private partnership and collaboration remains important to ensure that policymakers better
understand the state-of-play of AI authentication tooling. Demonstrating the progress and limitations of
various authentication methods will help inform policymakers as they consider relevant policy tools and
interventions in this area, and boost trust in and collaboration among the public and private sectors.
One technique alone will not be enough to ensure that a piece of content has or has not been created
by AI. Policymakers should ensure any regulatory approach takes into account the variety of techniques
needed for effective content authentication. Similar to the zero-trust model in cybersecurity, there
should be a mixed-method approach to AI authentication utilizing the techniques discussed above.
6
7
Incent and augment humans in the loop.
Invest in the development of clear, voluntary industry
consensus standards for AI Authentication.
Human authentication is essential to minimizing the potential negative impacts of AI tools and content,
particularly when it comes to unintended risks. Policy and research should explore how human authentication
can be augmented at the data, model, and output levels as described (including with the help of AI).
While industry is working on the development of standards and regimes to watermark and apply
provenance to and authenticate AI content, involvement from voluntary technical standards organizations,
like the International Standards Organization (ISO) and experts from bodies like NIST to help develop and
test best practices will help promote consistency, interoperability, and collaboration across techniques.