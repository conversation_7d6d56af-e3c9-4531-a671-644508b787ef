"""
Utility script to process the provided dataset documents using the analysis framework.
"""

import json
from pathlib import Path
from document_analyzer.core.task_manager import Analysis<PERSON>askManager, BatchAnalysisConfig
from document_analyzer.pipeline.integration import DataPipelineIntegrator

def process_dataset(dataset_dir: str = "dataset", output_dir: str = "analysis_results"):
    """Process all documents in the dataset directory."""
    
    dataset_path = Path(dataset_dir)
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Initialize components
    integrator = DataPipelineIntegrator()
    config = BatchAnalysisConfig(
        mode="unified",
        max_workers=4,
        validate_results=True,
        save_results=True
    )
    task_manager = AnalysisTaskManager(config)
    
    # Process each subdirectory (China, US)
    for country_dir in dataset_path.iterdir():
        if country_dir.is_dir():
            print(f"\nProcessing {country_dir.name} documents...")
            
            # Load documents from directory
            documents = list(integrator.load_from_directory(
                country_dir, 
                pattern="*.txt"
            ))
            
            print(f"Found {len(documents)} documents in {country_dir.name}")
            
            # Create analysis jobs
            job_ids = task_manager.process_document_batch(
                documents, 
                mode="unified"
            )
            
            # Execute jobs
            print(f"Executing {len(job_ids)} analysis jobs...")
            results = task_manager.execute_batch_jobs(job_ids)
            
            # Report results
            successful = sum(1 for success in results.values() if success)
            print(f"Completed {successful}/{len(job_ids)} jobs successfully")
            
            # Export results for this country
            country_output = output_path / f"{country_dir.name.lower()}_analysis.json"
            task_manager.export_results(country_output, status_filter="completed")
            print(f"Results saved to {country_output}")
    
    # Generate overall statistics
    stats = task_manager.get_job_statistics()
    stats_file = output_path / "processing_statistics.json"
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"\nProcessing complete! Statistics saved to {stats_file}")
    print(f"Overall success rate: {stats['success_rate']:.2%}")

def generate_sample_prompts(num_samples: int = 5):
    """Generate sample prompts from dataset documents."""
    
    dataset_path = Path("dataset")
    samples_output = Path("sample_prompts")
    samples_output.mkdir(exist_ok=True)
    
    integrator = DataPipelineIntegrator()
    
    # Get a few sample documents from each country
    sample_count = 0
    
    for country_dir in dataset_path.iterdir():
        if country_dir.is_dir() and sample_count < num_samples:
            documents = list(integrator.load_from_directory(
                country_dir, 
                pattern="*.txt"
            ))
            
            # Take first document as sample
            if documents:
                doc = documents[0]
                print(f"Generating sample prompt for {doc.doc_id}")
                
                # Generate unified prompt
                prompts = integrator.create_batch_prompts(
                    [doc], 
                    mode="unified"
                )
                
                # Save prompt
                prompt_file = samples_output / f"{doc.doc_id}_unified_prompt.txt"
                with open(prompt_file, 'w', encoding='utf-8') as f:
                    f.write(prompts[0])
                
                sample_count += 1
    
    print(f"Generated {sample_count} sample prompts in {samples_output}")

if __name__ == "__main__":
    print("Document Analysis Framework - Dataset Processor")
    print("=" * 50)
    
    # Generate sample prompts first
    print("Generating sample prompts...")
    generate_sample_prompts(3)
    
    # Process full dataset (commented out for demo)
    # Uncomment the line below to process the full dataset
    # process_dataset()
    
    print("\nDataset processing complete!")