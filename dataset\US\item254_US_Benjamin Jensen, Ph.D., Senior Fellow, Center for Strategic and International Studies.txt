  STATEMENT OF <PERSON><PERSON><PERSON><PERSON><PERSON>, PhD, SENIOR FELLOW, CENTER FOR 
  STRATEGIC AND INTERNATIONAL STUDIES, AND PROFESSOR, MARINE 
        CORPS UNIVERSITY, SCHOOL OF ADVANCED WARFIGHTING

    <PERSON><PERSON> <PERSON><PERSON> straw, Senator.
    Chairman <PERSON>, Vice Chairman <PERSON><PERSON><PERSON>, distinguished Members 
of the Committee, I really am honored today to sit with you and 
share my thoughts on what I think you all agree, from reading 
all the work that you've done on it, is probably the most 
important question facing our Nation from a technological 
perspective.
    The magnitude of the moment is clear, right? Both the 
Senate and the House are very much cultivating a national 
dialogue, and I just want to open as a citizen by thanking you 
for that. You have a powerful role in that, and so doing this 
here right now is key. And so, I have to be very blunt and 
clear with you that I'm going to talk to you less about the 
threat outside, Sir. I'm going to talk to you more about how I 
think we could get it wrong.
    Today, as part of that ongoing dialogue. I really want to 
look at the often-invisible center of gravity behind any 
technology: people, bureaucracy, and data, and in particular, 
data infrastructure and architecture. Put simply, you get the 
right people in place, with permissive policies and 
computational power at scale, and you gain a position of 
advantage in modern competition. I'll just put it bluntly. In 
the twenty-first century, the general or spy who doesn't have a 
model by their side is basically as helpless as a blind man in 
a bar fight.
    So, let's start with people. Imagine a future analyst 
working alongside a generative AI model to monitor enemy cyber 
capabilities. The model shows the analyst signs of new 
adversary malware in targeting U.S. critical infrastructure. 
The analyst disagrees. The challenge we have is today our 
analysts can't explain why they disagree because they haven't 
been trained in basic data science and statistics. They don't 
know how to balance causal inference and decades of thinking 
about human judgment and intuition. And sadly, I'll be honest, 
our modern analytical tradecraft and even something close to 
me, professional military education, tends to focus on discrete 
case studies more than statistical patterns or trend analysis. 
In other words, if we unleash a new suite of machine learning 
capabilities without the requisite workforce to understand how 
to use them, we're throwing good money after bad. And we really 
have to be careful about this. I can't stress this enough. If 
you don't actually make sure that people understand how to use 
the technology, it's just a magic box.
    Let's turn to the bureaucracy.
    Now, I want you to imagine what the Cuban Missile Crisis 
would look like in 2030: all sides with a wide range of machine 
learning capabilities. There would be an utter tension as 
machines wanted to speed up decision-making in the crisis. But 
senior decision-makers needed to slow it down to the pace of 
interagency collaboration. Even worse, you would be overwhelmed 
by deep fakes and computational propaganda pressuring you as 
elected officials and any senior leader to act. And pressure to 
act at a moment of crisis doesn't necessarily lead for sound 
decision-making. Unfortunately, neither our modern national 
security enterprise nor the bureaucracy surrounding government 
innovation/experimentation are ready for this world. If the 
analyst and military planner struggles to understand 
prediction, inference, and judgment through algorithms, the 
challenge is even more acute with senior decision-makers.
    At this level, most international relations and diplomatic 
history tells us that the essence of decision is as much 
emotion, flawed analogies, and bias as it is rational 
interests. What happens when irrational humans collide with 
rational algorithms during a crisis? Confusion could easily 
eclipse certainty, unleashing escalation and chaos.
    There are even larger challenges associated with creating a 
bureaucracy capable of adapting algorithms during a crisis. 
Because of complexity and uncertainty, all models require a 
constant stream of data to the moment at hand, not just the 
moment of the past. But crises are different than necessarily 
what preceded them. Unfortunately, slow adapters will succumb 
to quick deaths on that future battlefield. As a result, a 
culture of experimentation and constant model refinement will 
be the key to gaining and sustaining relative advantage. Now, 
ask yourself, do we have that bureaucracy?
    Last, consider data, architecture, and infrastructure, how 
we put the pieces of the puzzle together. I want you to imagine 
we're almost back to the old days of the SCUD hunt, right? 
Imagine the hunt for a mobile missile launcher in a future 
crisis. A clever adversary, knowing they were being watched, 
could easily poison the data used to support intelligence 
analysis and targeting. They could trick every computer model 
into thinking a school bus was a missile launcher, causing 
decision-makers to quickly lose confidence in otherwise 
accurate data. Even when you were right 99 percent of the time, 
the consequences of being wrong once are still adding unique 
human elements to crisis decision-making. Artificial 
intelligence and machine learning, therefore, are only as 
powerful as the underlying data. Yet to collect, process, and 
store that data is going to produce significant costs going 
forward. This is not going to be cheap. Furthermore, bad 
bureaucracy and policy can kill great models if they limit the 
flow of data.
    Last, let's talk about the fact that Prometheus has already 
shared the fire, and I think you all know that even from your 
opening comments, Chairman. Adversaries now into the 
foreseeable future can attack us at machine speed through a 
constant barrage of cyber operations and more disconcerting, 
mis-, dis- and mal-information, alongside entirely new forms of 
swarming attacks that could hold not just our military, but our 
civilian population at risk. Unless the United States is able 
to get the right mix of people, bureaucratic reform, and data 
infrastructure in place, those attacks could test the very 
foundation of our Republic.
    Now, I'm an optimist, so I'm going to be honest with you. 
I'm confident the United States can get it right. In fact, the 
future is ours to lose. Authoritarian regimes are subject to 
contradictions that make them rigid, brittle, and closed to new 
information. Look no further than regulations about adherence 
to socialist thought in data sets. These regimes are afraid to 
have the type of open, honest dialogue this Committee is 
promoting. And that fear is our opportunity.
    Thank you for the opportunity to testify.
    [The prepared statement of the witness follows:]
    [GRAPHICS NOT AVAILABLE IN TIFF FORMAT]
    
   STATEMENT OF YANN LeCUN, PhD, VICE PRESIDENT AND CHIEF AI 
  SCIENTIST, META PLATFORMS, AND SILVER PROFESSOR OF COMPUTER 
         SCIENCE AND DATA SCIENCE, NEW YORK UNIVERSITY

    Dr. LeCun. Chairman Warner, Vice Chairman Rubio, and 
distinguished Members of the Committee. Thank you for the 
opportunity to appear before you today to discuss important 
issues regarding AI.
    My name is Yann LeCun. I'm currently the Silver Professor 
of Computer Science and Data Science at New York University. 
I'm also Meta's Chief AI Scientist and co-founder of Meta's 
Fundamental AI Research Lab. At Meta, I focus on AI research, 
development strategy, and scientific leadership.
    AI has progressed leaps and bounds since I began my 
research career in the 1980s. Today, we are witnessing the 
development of generative AI, and in particular, large language 
models. These systems are trained through self-supervised 
learning. Or more simply, they are trained to fill in the 
blanks. In the process of doing so, those AI models learn to 
represent text or images--including the meaning, style, and 
syntax--in multiple languages. The internal representation can 
then be applied to downstream tasks such as translation, topic 
classification, et cetera. It can also be used to predict the 
next words in a text, which allow LLMs to answer questions or 
write essays, and write code as well. It is important not to 
undervalue the far-reaching potential opportunities they 
present. The development of AI is as foundational as the 
creation of the microprocessor, the personal computer, the 
Internet, and the mobile device. Like all foundational 
technologies, there will be a multitude of uses of AI. And like 
every technology, AI will be used by people for good and bad 
ends.
    As AI systems continue to develop, I'd like to highlight 
two defining issues. The first one is safety, and the second 
one is access. One way to start to address both of these issues 
is through the open sharing of current technology and 
scientific information. The free exchange of scientific papers, 
code, and trained models in the case of AI has enabled American 
leadership in science and technology. This concept is not new. 
It started a long time ago. Open sourcing technology has 
spurred rapid progress in systems we now consider basic 
infrastructure, such as the Internet and mobile communication 
networks.
    This doesn't mean that every model can or should be open. 
There is a role for both proprietary and open-source AI models. 
But an open-source basic model should be the foundation on 
which industry can build a vibrant ecosystem. An open-source 
model creates an industry standard, much like the model of the 
Internet in the mid '90s. Through this collaborative effort, AI 
technology will progress faster, more reliably, and more 
securely.
    Open sourcing also gives businesses and researchers access 
to tools that they could not otherwise build by themselves, 
which helps create a vast social and economic set of 
opportunities. In other words, open sourcing democratizes 
access. It gives more people and businesses the power to build 
upon state-of-the-art technology and to remedy potential 
weaknesses. This also helps promote democratic values and 
institutions, minimize social disparities, and improve 
competition. We want to ensure that the United States and 
American companies, together with other democracies, lead in AI 
development ahead of our adversaries, so that the foundational 
models are developed here and represent and share our values. 
By open sourcing current AI tools, we can develop our research 
and development ecosystem faster than our adversary.
    As AI technology progresses, there is an urgent need for 
governments to work together, especially democracies, to set 
common AI standards and governance models. This is another 
valuable area where we welcome working with regulators to set 
appropriate transparency requirements, red teaming standards, 
and safety mitigations to help ensure those codes of practice, 
standards, and guardrails are consistent across the world. The 
White House's voluntary commitment is a critical step in 
ensuring responsible guardrails, and they create a model for 
other governments to follow. Continued U.S. leadership by 
Congress and the White House is important in ensuring that 
society can benefit from innovation in AI while striking the 
right balance with protecting rights and freedom, preserving 
national security interests, and mitigating risks where those 
arise.
    I'd like to close by thanking Chairman Warner, Vice 
Chairman Rubio, and the other Members of the Committee for your 
leadership. At the end of the day, our job is to work 
collaboratively with you, with Congress, with other nations, 
and with other companies in order to drive innovation and 
progress in a manner that is safe and secure and consistent 
with our national security interests.
    Thank you. I look forward to your questions.
    [The prepared statement of the witness follows:]
    [GRAPHICS NOT AVAILABLE IN TIFF FORMAT]
    
    STATEMENT OF JEFFREY DING, PhD, ASSISTANT PROFESSOR OF 
        POLITICAL SCIENCE, GEORGE WASHINGTON UNIVERSITY

    Dr. Ding. Chairman Warner, Vice Chairman Rubio, and Members 
of the Committee. I am honored by the opportunity to brief this 
Committee on the National Security Implications of AI. In all 
honesty, I also have a selfish reason for attending today. I 
teach political science at GW, and my students all really look 
up to the Committee Members in this room and also all the staff 
who are working behind the scenes to put this hearing together. 
So, when I got to tell the class this morning that I was doing 
this testimony, they all got the most excited I've ever seen 
them get excited this semester. And so, hopefully, that will 
cause them to do more of the required readings in class. In all 
seriousness, I have great students, I'm very grateful to be 
here.
    Today, in my opening remarks, I want to make three main 
points from my written testimony. The first is when it comes to 
the national security of implications of AI, the main driver 
and the main vector is which country will be able to sustain 
productivity growth at higher levels than their rivals. And for 
this vector, the distinction between innovation capacity and 
diffusion capacity is central to thinking about technological 
leadership in AI. Today, when various groups--whether that be 
experts, policymakers, the Intelligence Community--when they 
try to assess technological leadership, they are overly 
preoccupied with innovation capacity. Which state is going to 
be the first to generate new-to-the-world breakthroughs, the 
first to generate that next leap in large language models. They 
neglect diffusion capacity. A state's ability to spread and 
adopt innovations after their initial introduction across 
productive processes.
    And that process of diffusion throughout the entire economy 
is really important for technologies like AI. If we were 
talking about a sector like automobiles, or even a sector like 
clean energy, we might not be talking as much about the effects 
of spreading technologies across all different productive 
processes throughout the entire economy. AI is a general-
purpose technology, like electricity, like the computer, like 
my fellow panelists just mentioned in his testimony. And 
general-purpose technologies historically precede waves of 
productivity growth because they can have pervasive effects 
throughout the entire economy. So, the U.S. in the late 19th 
century became the leading economic power before it translated 
that influence into military and geopolitical leadership, 
because it was better at adopting general purpose technologies 
at scale, like electricity, like the American system of 
interchangeable manufacture, at a better and a more effective 
rate than its rivals.
    Point number two is when we assess China's technological 
leadership and use this framework of innovation capacity versus 
diffusion capacity, my research finds that China faces a 
diffusion deficit. Its ability to diffuse innovations like AI 
across the entire economy lags far behind its ability to 
pioneer initial innovations or make fundamental breakthroughs.
    And so, when you've heard from other people in the past or 
in the briefing memos you are reading, you are probably getting 
a lot of innovation-centric indicators of China's scientific 
and technological prowess: its advances in R&D spending, 
headline numbers on patents and publications. In my research, 
I've presented evidence about China's diffusion deficit by 
looking at how is China actually adopting other information and 
communications technologies at scale? What are its adoption 
rates in cloud computing, industrial software, related 
technologies that would all be in a similar category to AI? And 
those rates lag far behind the U.S.
    Another indicator would be how is China's ability to widen 
the pool of average AI engineers? I'm not talking about Nobel 
Prize of computing winners like my fellow panelists here, but 
just average AI engineers who can take existing models and 
adapt them in particular sectors or industries or specific 
applications. And based on my data, China has only 29 
universities that meet a baseline quality metric for AI 
engineering, whereas the U.S. has 159. So, there's a large gap 
in terms of China's diffusion capacity compared to its 
innovation capacity in AI.
    I'll close with the third point, which is some recent 
trends in Chinese labs' large language models. China has built 
large language models similar to OpenAI's ChatGPT, as well as 
OpenAI's text-to-image models like DALL-E. But there's still a 
large gap in terms of Chinese performance on these models. And, 
in fact, on benchmarks and leaderboards where U.S. models are 
compared to Chinese models on Chinese language prompts, models 
like ChatGPT still perform better than Chinese counterparts. 
Some of these bottlenecks relate to a reliance on Western 
companies to open up new paradigms, China's censorship regime, 
which Dr. Jensen talked about, and computing power bottlenecks, 
which I'm happy to expand on further.
    I'll close by saying I submitted three specific policy 
recommendations to the Committee. But I want to emphasize one, 
which is keep calm and avoid overhyping China's AI 
capabilities. In the paper that forms the basis for this 
testimony, I called attention to a 1969 CIA assessment of the 
Soviet Union's technological capabilities. It was remarkable 
because it went against the dominant narrative of the time of a 
Soviet Union close to overtaking the U.S. in technological 
leadership. The report concluded that the technological gap was 
actually widening between the U.S. as the leader and the Soviet 
Union because of the U.S.'s superior mechanisms to spread 
technologies and diffuse technologies. Fifty years later, we 
know why this assessment was right, and we know we have to 
focus on diffusion capacity when it comes to scientific and 
technological leadership.
    Thanks for your time.