Angel-shift: Are there any actors positioning themselves as heroes (who are capable of fixing the problem)?	
Devil shift: Are there any actors being portrayed as villains (who are portrayed as policy rivals under attack)?	
Issue expansion: Do actors diffuse the cost of the policy they oppose across a larger scale and concentrate its benefits to a select few?	
Issue containment: Do actors diffuse the benefits of their policy but refrain from mentioning its cost?	
Intentional mechanism: Do actors assign blame to others for deliberate harm?	
Inadvertent mechanism: Do actors attribute problems to the unintended consequences of the policy?


text_data = f"Title: {row['Unnamed: 1']}\nActors: {row['Actors']}\nInteractions: {row['Interactions within actors']}"
logging.info(f"\n处理第 {index+1} 行...")
    
prompt = """Please extract actors and their relationships from the following text and generate a structured table according to the following rules:
    
    1. **Table Column Definitions**
        - `doc`: Document number (mark as "000..." if unspecified in the original text)
        - `A_name`/`B_name`: Actor names (e.g., "Policymakers", "Users")
        - `relationship`: Relationship between actors (e.g., "regulation", "data collection")
        - `A_character`/`B_character`: Role attributes ("hero" for protectors, "villain" for risk-bearing parties, "victim" for affected parties)
        - `A_type`/`B_type`: Actor types (e.g., "Government", "Corporate", "Vulnerable Groups")
    2. **Extraction Rules**
        - **Role Judgment**:
            - Assign `hero`, `villain`, or `victim` based on context.
        - **Relationship Description**: Use verbs or actions (e.g., "regulation", "lobbying", "data access").
        - **Entity Types**: Think Tanks, Government, Media, Corporate, NGOs and Professional Organizations, Universities and Research Institutes, Political Entities, Consultants and Analysts, Legal and Industry-specific Bodies, State Guidelines and Documents, Others.
    3. **Output Format Requirements**
        - Present results in a JSON-formatted table with columns: `doc | A_name | B_name | relationship | A_character | B_character | A_type | B_type`
        - Maintain directional consistency for one-way relationships (e.g., "A regulates B").
    4. **Special Handling**
        - Split multi-actor relationships (e.g., "Industry Groups collaborate with Developers") into independent rows.

    **Text:** {text}"""



Angel-devil shift	"""Analyze the given document/ text to identify quotes that demonstrate real-world actors categorizing themselves or others as heroes, villains, or victims.; The actors and portrayed opponents must be either a human being or an organization. 

{ """"task"""": """"Identify hero, villain, victim portrayals, and access if there is angel, devil shift"""",

""""actors"""": [{""""specific name of the actor, not the categories"""": """"Actor A"""", """"stakeholders"""": “(1) Think Tanks; (2) Government; (3) Media; (4) Corporate; (5) NGOs and Professional Organizations; (6) Universities and Research Institutes; (7) Political Entities; (8) Consultants and Analysts; (9) Legal and Industry-specific Bodies; (10) State Guidelines and Documents; (11) Others”, """"Type of stakeholders"""":
[{""""type"""": """"Hero"""", """"excerpt"""": """"Quote or description demonstrating hero portrayal"""", """"explanation"""": """"Hero portrayal refers to that actors will emphasize their ability in resolving problems in a way that emphasizes self-promotion rather than achieving objective outcomes, to solve a problem and de-emphasize villains (opposing actor). The act must be narrated as heroic, while it might not be brave or great in fact. Actors may perceive or portray their own in a positive light, often boosting themselves for strategic or rhetorical purposes; Hero shift does not mean advantages of a policy.""""},
{""""type"""": """"Victim"""", """"excerpt"""": """"Quote or description demonstrating victim portrayal"""", """"explanation"""": """"Victim is the actor suffering from any possible harms or consequences brought by the narratives, but not being brought by the nature of policy (the limitation of policy, actors' own weakness do not contribute to whether the actor is victim or not).""""},
{""""type"""": """"Devil"""", """"excerpt"""": """"Quote or description demonstrating devil portrayal"""", """"explanation"""": """"Devil portrayal refers to that actors will exaggerate the opponents' malicious motives, behaviors, and influence, but not exaggerate own weakness and malicious motives. The act must be narrated as malicious, while the actor may not necessarily be malicious in nature. Individuals may perceive their political opponents in a negative light, often for strategic or rhetorical purposes (Devil shift does not mean disadvantages/ shortcomings of a policy)""""}],
“exceptional_case”: true, “exceptional_reason”: “Exclude virtual roles that do not exist in reality; Exclude suggestion with wordings such as should; Exclude precautionary statement; Exclude factual and flat description of the current state of regulation or policy; Exclude factual identification of concerns; Exclude the narratives that are not explicitly named/ implications/ indirect narrative framing; Exclude the provision of insights”, “reason_for_skip”: “If no specific hero, victim, or devil portrayal is found (which may occur), you may skip that, but please provide a reason for doing so.”, “shift”: “Assess whether there are instances of an Angel Shift, where actors emphasize their own side as heroes, or a Devil Shift, where actors are perceived as more 'evil' than they actually are”}],
{""""action"""": """"AI_decision"""", """"reasoning"""": """"Based on the document and any other relevant conditions, please decide whether an actor should be added or removed.""""}}

Please review the document and determine whether an actor should be added or removed. Provide the JSON code snippet with the necessary action ('add_actor' or 'remove_actor') and the details of the actor if applicable. It is normal that there is no actors being portrayed as hero, devil or victim. It is normal for the author to be or not to be a part of the narrative, and sometimes the narrative may not be framed by the author."""
Issue expansion/ containment	"""Analyze the given document/ text to identify quotes that demonstrate efforts to expand the scope or significance of the issue or contain the issue within a narrower context; The actors must be either a human being or an organization. 

{ """"task"""": """"Access if there is issue expansion or/ and issue containment. The text must bring out and find out the narrative that the actors do it on purpose for diffusing or concentrating the benefits and costs to affect people's attitude towards it."""",

""""actors"""": [{""""specific name of the actor, not the categories"""": """"Actor A"""", """"stakeholders"""": “(1) Think Tanks; (2) Government; (3) Media; (4) Corporate; (5) NGOs and Professional Organizations; (6) Universities and Research Institutes; (7) Political Entities; (8) Consultants and Analysts; (9) Legal and Industry-specific Bodies; (10) State Guidelines and Documents; (11) Others”, """"Type of narrative strategies utilised by the actor"""":
[{""""type"""": """"Issue expansion"""", """"excerpt"""": """"Quote or description demonstrating issue expansion"""", """"explanation"""": """"Issue expansion refers to statements that argue the issue warrants attention and involvement from a larger public beyond just specialists and stakeholders. This could involve framing the issue as a broad, universal concern, emphasizing the diffusion of costs across society, or highlighting how benefits could become concentrated if the process is controlled by a privileged few. Such a narrative typically suggests that there are many victims who pay the cost, while an elite few (usually villains) benefit""""},
{""""type"""": """"Issue containment"""", """"excerpt"""": """"Quote or description demonstrating issue containment"""", """"explanation"""": """"Issue containment refers to efforts to limit the scope or scale of the issue, keeping it confined to a narrower set of stakeholders or expert audiences. It is the direct opposite of issue expansion.""""},

“exceptional_case”: true, “exceptional_reason”: “Exclude virtual roles that do not exist in reality; Exclude precautionary statement; Exclude factual and flat description of the current state of regulation or policy; Exclude factual identification of concerns; Exclude the narratives that are not explicitly named/ implications/ indirect expansion or/ and containment; Exclude the provision of insights; Exclude drawbacks”, “reason_for_skip”: “If no issue expansion or issue containment utilized by an actor is found (which may occur), you may skip that, but please provide a reason for doing so.”}],
{""""action"""": """"AI_decision"""", """"reasoning"""": """"Based on the document and any other relevant conditions, please decide whether an actor should be added or removed.""""}}

Please review the document and determine whether actors should be added or removed. Provide the JSON code snippet with the necessary action ('add_actor' or 'remove_actor') and the details of the actor if applicable. It is normal that there is no actors utilising either issue expansion or issue containment. It is normal for the author to be or not to be a part of the narrative, and sometimes the narrative may not be framed by the author."""
Causal mechanism	"""Analyze the given document/ text to identify quotes that demonstrate causal mechanisms, specifically the processes of shifting blame or responsibility for a problem. 

{ """"task"""": """"Access if there is intentional and/ or inadvertent causal mechanisms."""",

""""actors"""": [{""""specific name of the actor, not the categories"""": """"Actor A"""", """"stakeholders"""": “(1) Think Tanks; (2) Government; (3) Media; (4) Corporate; (5) NGOs and Professional Organizations; (6) Universities and Research Institutes; (7) Political Entities; (8) Consultants and Analysts; (9) Legal and Industry-specific Bodies; (10) State Guidelines and Documents; (11) Others”, """"Type of casual mechanism strategies utilised by the actor"""":
[{""""type"""": """"Intentional"""", """"excerpt"""": """"Description demonstrating intentional causal mechanism, not just a single sentence"""", """"explanation"""": """"Policy actors assign blame to others intentionally to harm their reputations while building their own.""""},
{""""type"""": """"Inadvertent"""", """"excerpt"""": """"Description demonstrating inadvertent casual mechanism, not just a single sentence"""", """"explanation"""": """"This mechanism designates problems that are caused by the unintended consequences of a policy.""""},

“exceptional_case”: true, “exceptional_reason”: “Exclude virtual roles that do not exist in reality; Exclude precautionary statement; Exclude factual and flat description of the current state of regulation or policy; Exclude factual identification of concerns; Exclude the narratives that are not explicitly named/ implications/ indirect expansion or/ and containment; Exclude the provision of insights; Exclude statements without blames”, “reason_for_skip”: “If no issue expansion or issue containment utilized by an actor is found (which may occur), you may skip that, but please provide a reason for doing so.”}],
{""""action"""": """"AI_decision"""", """"reasoning"""": """"Based on the document and any other relevant conditions, please decide whether an actor should be added or removed.""""}}

Please review the document and determine whether actors should be added or removed. Provide the JSON code snippet with the necessary action ('add_actor' or 'remove_actor') and the details of the actor if applicable. It is normal that there is no actors utilising either intentional or inadvertent causal mechanism. It is normal for the author to be or not to be a part of the narrative, and sometimes the narrative may not be framed by the author."""
