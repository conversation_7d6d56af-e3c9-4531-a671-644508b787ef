# Document Analysis Framework

A comprehensive Python framework for analyzing documents to extract actors, relationships, portrayals, issue scope strategies, and causal mechanisms using structured prompts and validation.

## Features

- **Multi-mode Analysis**: Unified comprehensive analysis or focused task-specific analysis
- **Structured Output**: JSON schema validation ensures consistent, parseable results
- **Batch Processing**: Process multiple documents efficiently with parallel execution
- **Pipeline Integration**: Load documents from CSV, JSON, or directory structures
- **Quality Control**: Built-in validation for JSON structure, actor references, and evidence quotes
- **Workflow Orchestration**: High-level workflows for common analysis patterns

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd document_analyzer

# Install dependencies
pip install jsonschema
```

## Quick Start

### Basic Single Document Analysis

```python
from document_analyzer import DocumentAnalyzer

# Initialize analyzer
analyzer = DocumentAnalyzer()

# Create analysis request
request = analyzer.create_analysis_request(
    doc_id="sample_001",
    title="AI Policy Document", 
    text="The FTC announced new AI regulations...",
    optional_known_actors=["FTC", "OpenAI"]
)

# Generate analysis prompt
prompt = analyzer.get_unified_prompt(request)

# Send prompt to your LLM and validate response
json_response = "..." # Response from LLM
is_valid, error, data = analyzer.validate_analysis_result(json_response, request.text)
```

### Batch Processing

```python
from document_analyzer.core.task_manager import AnalysisTaskManager
from document_analyzer.pipeline.integration import DocumentRecord

# Create document records
documents = [
    DocumentRecord("doc1", "Title 1", "Document text...", actors=["Actor1"]),
    DocumentRecord("doc2", "Title 2", "Document text...", actors=["Actor2"])
]

# Process batch
task_manager = AnalysisTaskManager()
job_ids = task_manager.process_document_batch(documents, mode="unified")
results = task_manager.execute_batch_jobs(job_ids)
```

### Load from Data Sources

```python
from document_analyzer.pipeline.integration import DataPipelineIntegrator

integrator = DataPipelineIntegrator()

# Load from CSV
documents = list(integrator.load_from_csv("documents.csv"))

# Load from directory
documents = list(integrator.load_from_directory("./docs/", "*.txt"))

# Generate prompts
prompts = integrator.create_batch_prompts(documents, mode="portrayals")
```

## Analysis Modes

### Unified Mode (Recommended)
Comprehensive analysis extracting all components in a single pass:
- Actors and relationships
- Portrayals (hero/victim/devil)
- Issue scope strategies
- Causal mechanisms

### Task-Specific Modes
Focus on specific aspects for reduced complexity:
- `entities`: Actor and relationship extraction only
- `portrayals`: Hero/victim/devil detection and angel/devil shifts
- `issues`: Issue expansion/containment strategies
- `causal`: Intentional/inadvertent causal mechanisms

## Core Components

### DocumentAnalyzer
Main analysis engine that generates prompts and validates results.

### AnalysisTaskManager  
Orchestrates batch processing with parallel execution and job tracking.

### DataPipelineIntegrator
Handles loading documents from various sources (CSV, JSON, directories).

### JSONValidator
Validates analysis results against the defined schema.

## Output Schema

All analysis results follow a structured JSON schema:

```json
{
  "doc_id": "string",
  "title": "string|null", 
  "actors": [{"name": "string", "stakeholder_category": "...", "notes": "..."}],
  "relationships": {"skipped": boolean, "items": [...]},
  "portrayals": {"skipped": boolean, "items": [...], "shifts": {...}},
  "issue_scope": {"skipped": boolean, "items": [...]},
  "causal_mechanisms": {"skipped": boolean, "items": [...]},
  "ai_decisions": [{"action": "...", "actor": "...", "reasoning": "..."}]
}
```

## Quality Control

- **JSON Schema Validation**: Ensures output matches expected structure
- **Actor Reference Validation**: Checks that all referenced actors exist in the actors array
- **Evidence Validation**: Optionally validates that evidence quotes exist in source text
- **Enum Validation**: Ensures categorical values match allowed options

## Examples

See `examples.py` for comprehensive usage examples including:
- Basic single document analysis
- Batch processing workflows
- Pipeline integration patterns
- Validation examples
- Directory processing

## Configuration

### BatchAnalysisConfig
Configure batch processing behavior:
- `mode`: Analysis mode to use
- `max_workers`: Parallel execution threads
- `retry_attempts`: Number of retry attempts for failed jobs
- `validate_results`: Enable/disable result validation

## Integration Guidelines

### LLM Integration
The framework generates prompts but doesn't call LLMs directly. Integrate with your preferred LLM service:

```python
# Generate prompt
prompt = analyzer.get_unified_prompt(request)

# Call your LLM service
response = your_llm_service.complete(prompt, temperature=0.1)

# Validate response
is_valid, error, data = analyzer.validate_analysis_result(response, request.text)
```

### Production Deployment
- Use temperature near 0 for deterministic outputs
- Implement retry logic for malformed JSON responses
- Set appropriate max_tokens based on document size
- Monitor validation failure rates
- Cache common actor classifications for consistency

## Performance Optimization

- **Parallel Processing**: Use `AnalysisTaskManager` for concurrent job execution
- **Mode Selection**: Use task-specific modes for focused analysis to reduce latency
- **Batch Operations**: Process multiple documents together for efficiency
- **Result Caching**: Store and reuse analysis results where appropriate

## Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive examples for new features
3. Ensure all new code includes proper validation
4. Update documentation for any API changes

## License

[Specify your license here]