The AI Regulatory Alignment Problem

Policy Recommendations Our analysis supports four concrete recommendations. First, adverse event reporting—both mandatory and voluntary—can address a central impediment to effective AI regulation—the lack of reliable information about different AI systems and their risks. For instance, the government could require the transparent reporting of deleterious AI behavior, ranging from concrete harms (e.g., misdiagnosis by medical AI systems) to more abstract concerns (e.g., generation of biological pathogens). The agency managing the reporting system could then either refer incidents to existing agencies, or identify gaps if reports fall between existing authorities. Such a policy would require relatively few technical and institutional resources to operationalize and provide clear benefits. Previous experience with incident reporting systems (e.g., the Food and Drug Administration’s Adverse Event Reporting System and Cybersecurity and Infrastructure Security Agency reporting system) has shown the value of incident reporting for identifying threats and informing future regulation. Lightweight registration of models can be seen as a complement to adverse event reporting, enabling regulators to understand what models might be susceptible to similar risks. Second, government oversight of third-party auditors can enable the verification of industry claims without miring the government in direct auditing of AI systems. Third-party audits would ensure independent assessment of AI algorithms and models and verification of industry claims. Reducing conflicts of interest will be critical. Government oversight—similar to the Public Company Accounting Oversight Board—may encourage standardization in the third-party audit market and improve the quality of information available about AI systems and their performance. The AI Regulatory  Alignment Problem Proposals that strengthen authorities of existing agencies, including the many agencies already regulating AI, are more likely to successfully implement timely regulation than proposals reliant upon a new superregulatory agency. Third, proposals that strengthen authorities of existing agencies, including the many agencies already regulating AI, are more likely to successfully implement timely regulation than proposals reliant on a new superregulatory agency. Fourth, policymakers must grapple with the reality that regulatory regimes expose tensions between objectives (e.g., restrict foundation models versus democratize AI innovation) and values (e.g., fairness versus privacy) that cannot be avoided simply by demanding the technical community operationalize yet-to-be-determined standards and metrics. In sum, there is a role for reasonable government action to govern AI.  But AI’s regulatory alignment problem is a hard one. It will only be made harder if policymakers rush to regulate without regard for the feasibility or unintended consequences of their proposals.