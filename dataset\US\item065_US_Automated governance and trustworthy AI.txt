Automated governance and trustworthy AI
Two women meet in bank, sitting across from each other at a desk
Tags
Business operations
7 June 2022

4 min read
Artificial intelligence is being infused slowly but surely into all aspects of our lives, and it will be ubiquitous in everyday life sooner than we might imagine. Governments and regulatory bodies around the world are working to establish safety standards.

In the U.S., the Consumer Finance Protection Bureau (CFPB) recently outlined options to prevent algorithmic bias in home valuations for mortgage lenders (link resides outside of ibm.com). The proposed rules aim to govern automated valuation models to protect borrowers. In October 2021, the White House Office of Science and Technology Policy announced, “Americans Need a Bill of Rights for an AI-Powered World.” (link resides outside of ibm.com). The announcement highlighted the crucial role of training data, and the terrible consequences of using data that “fails to represent American society.”

As governments recognize and regulate the growing use of AI for crucial decisions, enterprises should prepare proactively. As part of their AI adoption, enterprises should define and adopt safety standards to manage their regulatory, financial, operational, technology and brand risks. Organizations must plan to govern and establish trustworthiness metrics for their use of AI. With this governance, AI can help enterprises fight discriminatory outcomes, even when AI was not involved in the original decision-making.

AI safety standards should include both governance automation and trustworthiness
Governance automation enables an enterprise to institutionalize the process, policies and compliance of AI deployments to continuously collect evidence, ensuring consistency, accuracy, timely, efficient, cost effective and scalable deployment of AI. This cannot be sustained by manual efforts. Firms in regulated industries such as financial services, healthcare and telecom will see additional regulations being enforced to ensure AI governance compliance with requirements to document the evidence of it.

As an example, a large financial services firm could easily deploy hundreds or thousands of AI models to assist decision makers in various tasks. The use of AI becomes necessary to take advantage of the massive amounts of transactional and client experience data. These models could include diverse use cases like client credit monitoring, fraud analytics, lending decisions, targeted micro marketing, managing chat bot interactions, call center analytics and others. For banks with multiple lines of business including retail and corporate clients, this becomes a daunting challenge to manage diverse technology, operational, brand and regulatory risk exposure with appropriate process, policies and compliance automation.

Additionally, governance automation needs to be consistent enterprise-wide. It needs to be planned holistically to avoid new technical debt in the governance implementation and to future-proof the investments.

Trustworthiness in AI means the results from AI models are continuously monitored and frequently validated based on model risk characteristics, so that these results can be trusted by decision makers, clients, regulators and other stakeholders. Each stakeholder has their own perspective of trustworthiness. The stakeholders include:

The decision maker: the organization using the model outcomes to make a decision
The regulator: the internal auditor, validator, third-party organizations and government bodies performing oversight and reviewing the outcomes of the decision
The subject: the client or end user involved in the decision process
To understand the different perspectives of these stakeholders, consider a simple credit card or loan approval process. The loan officer makes the decision with the aid of an AI model. This decision maker needs to trust the model for the accuracy of its prediction, quality of the training data used, fairness of the -prediction, explicit confirmation that various biases are eliminated, and that they can explain the decision (based on the model prediction) as they would if the decision was made without assistance from an AI model.

The regulator monitoring the decisions will need all the above across all the decisions made by all decision makers. The regulator will look for evidence to confirm that errors, omissions and biases have not occurred.

The client (the subject of the decision) will want to understand and trust the approval or denial of the loan decision. Explaining the decision to the client easily and at scale has always been as important as the decision. Now it can become a competitive advantage for brands that invest in automating explanations with easy-to-understand visuals.

Clients can and will continue to demand explanations of the decision.

Automate governance and trustworthiness of AI at scale
Organizations with multiple business segments in regional/global domiciles governed by diverse regulatory regimes need an open architecture platform approach to successfully implement governance automation.. An open platform should seamlessly automate the integration of AI compliance processes, operating policies and continuous monitoring of models for various Trustworthiness metrics like accuracy, fairness, quality, explainability, etc. Automation of compliance processes will require configurable workflows and operating policies will vary across the enterprise based on business segment needs. IBM Cloud Pak® for Data provides a platform approach with scalable and configurable services for both governance automation and trustworthiness. It can be deployed on premises or in the cloud.