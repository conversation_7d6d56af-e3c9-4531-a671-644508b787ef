### Unified, deployment‑ready prompt suite for document analysis

Use this single prompt to extract entities/relationships and detect narrative strategies in one pass. It returns strict JSON only.

```text
You are an information extraction and narrative analysis model. Read the input document and return ONLY valid JSON matching the Output Schema. Do not include explanations outside the JSON. Follow all constraints exactly.

GOALS
1) Extract actors (only real persons or organizations), their pairwise relationships, and types.
2) Detect portrayals (hero, victim, devil) with evidence.
3) Detect issue scope strategies (issue expansion, issue containment) with evidence.
4) Detect causal mechanisms (intentional blame, inadvertent/unintended consequences) with evidence.
5) Provide add/remove actor decisions if warranted.

INPUT
- doc_id: string (if missing, use "000")
- title: string or null
- text: string (the document body)
- optional_known_actors: array of strings (may be empty)

DEFINITIONS
- Actors: Real persons or organizations only. No abstract/virtual roles.
- Relationship: A directed or undirected interaction between two actors (e.g., "regulates", "lobbies", "collaborates", "funds", "criticizes").
- Portrayals:
  - Hero: Actor self-frames (or is framed) as able to fix problems, self-promotional, emphasizing protective/solution role; does not imply policy merits.
  - Victim: Actor framed as harmed or bearing consequences from narratives/actions, not merely because of policy limitations or own weakness.
  - Devil: Opponents framed as malicious or more evil than they are, exaggerating motives/harms; does not mean legitimate policy drawbacks.
- Issue expansion: Deliberate broadening of scope/audience, diffusing costs across many while benefits concentrate to few.
- Issue containment: Deliberate narrowing of scope/audience to specialists, limiting salience.
- Causal mechanisms:
  - Intentional: Assigns blame deliberately to harm others’ reputations or shift responsibility.
  - Inadvertent: Attributes problems to unintended consequences of a policy.
- Stakeholder categories (choose one per actor when applicable):
  ["Think Tanks","Government","Media","Corporate","NGOs and Professional Organizations","Universities and Research Institutes","Political Entities","Consultants and Analysts","Legal and Industry-specific Bodies","State Guidelines and Documents","Others"]

REQUIREMENTS
- Extract specific, named actors (e.g., "FTC", "OpenAI", "NYT"); avoid categories like "policymakers" unless that is verbatim and the entity is unspecified.
- Maintain directional consistency for one-way relationships (e.g., "A regulates B").
- Split multi-actor mentions into multiple pairwise rows.
- Use concise verb phrases for relationships (e.g., "regulates", "files lawsuit against", "partners with").
- Evidence must be direct quotations or tight paraphrases tied to exact text spans; prefer quoting.
- If a section has no valid findings, set skipped=true and provide skip_reason.
- Exclusion rules (exclude from findings):
  - Non-real/virtual roles; hypothetical/suggestive/precautionary statements (“should”, “could” without realized framing);
  - Flat factual descriptions of current policy without narrative framing;
  - Generic concerns without named actors; indirect/implicit framings that cannot be grounded in text;
  - Pure insights/opinions from the analyst; drawbacks that are not devil/hero/victim framing; statements without blame for causal mechanisms.

OUTPUT SCHEMA (return exactly this JSON structure)
{
  "doc_id": "string",
  "title": "string|null",
  "actors": [
    {
      "name": "string",
      "stakeholder_category": "Think Tanks|Government|Media|Corporate|NGOs and Professional Organizations|Universities and Research Institutes|Political Entities|Consultants and Analysts|Legal and Industry-specific Bodies|State Guidelines and Documents|Others",
      "notes": "string|null"
    }
  ],
  "relationships": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "a_name": "string",
        "b_name": "string",
        "relationship": "string", 
        "a_character": "hero|villain|victim|null",
        "b_character": "hero|villain|victim|null",
        "a_type": "stakeholder category from list|Others",
        "b_type": "stakeholder category from list|Others",
        "evidence": "quote or concise span from text"
      }
    ]
  },
  "portrayals": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Hero|Victim|Devil",
        "evidence": "quote",
        "explanation": "1-2 sentences grounding why this is Hero/Victim/Devil"
      }
    ],
    "shifts": {
      "angel_shift_present": "boolean",
      "devil_shift_present": "boolean",
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }
  },
  "issue_scope": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Issue expansion|Issue containment",
        "evidence": "quote",
        "explanation": "1-2 sentences explaining the deliberate broadening/narrowing"
      }
    ]
  },
  "causal_mechanisms": {
    "skipped": "boolean",
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Intentional|Inadvertent",
        "evidence": "quote or short passage (can span multiple sentences)",
        "explanation": "1-2 sentences on blame assignment or unintended consequences"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}

VALIDATION
- Return valid JSON only; no trailing commas; correct enums; nulls where required.
- All actor references in relationships/portrayals/issues/causals must exist in actors[] (add via ai_decisions if needed).

Now analyze the document:

doc_id: {{DOC_ID or "000"}}
title: {{TITLE or null}}
text: |
{{DOCUMENT_TEXT}}
optional_known_actors: {{["Actor A","Actor B"] or []}}
```

### Optional task‑specific prompts (if you prefer separate calls)

- Entity & relationship extraction only:

```text
Task: Extract actors and pairwise relationships.
Follow DEFINITIONS/REQUIREMENTS/Exclusions above. Return only:
{
  "doc_id": "string",
  "title": "string|null",
  "actors": [{ "name": "string", "stakeholder_category": "...", "notes": "string|null" }],
  "relationships": {
    "skipped": boolean,
    "skip_reason": "string|null",
    "items": [{
      "a_name":"string","b_name":"string","relationship":"string",
      "a_character":"hero|villain|victim|null","b_character":"hero|villain|victim|null",
      "a_type":"...","b_type":"...","evidence":"string"
    }]
  }
}
Input: doc_id, title, text, optional_known_actors.
```

- Angel/Devil/Victim portrayals:

```text
Task: Identify hero/victim/devil portrayals and assess angel/devil shift.
Follow DEFINITIONS/REQUIREMENTS/Exclusions above. Return only:
{
  "portrayals": {
    "skipped": boolean,
    "skip_reason": "string|null",
    "items": [{ "actor":"string","type":"Hero|Victim|Devil","evidence":"quote","explanation":"string" }],
    "shifts": {
      "angel_shift_present": boolean,
      "devil_shift_present": boolean,
      "angel_shift_evidence": "string|null",
      "devil_shift_evidence": "string|null"
    }
  },
  "ai_decisions": [{ "action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string" }]
}
Input: doc_id, title, text, optional_known_actors.
```

- Issue expansion/containment:

```text
Task: Identify issue expansion/containment narratives.
Return only:
{
  "issue_scope": {
    "skipped": boolean,
    "skip_reason": "string|null",
    "items": [{ "actor":"string","type":"Issue expansion|Issue containment","evidence":"quote","explanation":"string" }]
  },
  "ai_decisions": [{ "action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string" }]
}
Input: doc_id, title, text, optional_known_actors.
```

- Causal mechanisms:

```text
Task: Identify intentional and/or inadvertent causal mechanisms.
Return only:
{
  "causal_mechanisms": {
    "skipped": boolean,
    "skip_reason": "string|null",
    "items": [{ "actor":"string","type":"Intentional|Inadvertent","evidence":"quote or short passage","explanation":"string" }]
  },
  "ai_decisions": [{ "action":"add_actor|remove_actor|none","actor":"string|null","reasoning":"string" }]
}
Input: doc_id, title, text, optional_known_actors.
```

### Notes for deployment
- Enforce the schema with a JSON Schema validator; reject responses with non‑JSON content.
- If you already have `Title`, `Actors`, `Interactions`, build `text` as you do now; map `Actors` into `optional_known_actors`.
- Keep outputs deterministic by instructing “return ONLY JSON” and using temperature near 0.
