"""
Example usage and test cases for the document analysis framework.
"""

import json
from pathlib import Path
from document_analyzer import <PERSON>umentAnaly<PERSON>
from document_analyzer.core.task_manager import AnalysisTaskManager, AnalysisWorkflow, BatchAnalysisConfig
from document_analyzer.pipeline.integration import DataPipelineIntegrator, DocumentRecord

def example_basic_usage():
    """Basic usage example for single document analysis."""
    print("=== Basic Usage Example ===")
    
    # Initialize the analyzer
    analyzer = DocumentAnalyzer()
    
    # Create a sample document
    sample_text = """
    The Federal Trade Commission (FTC) announced new regulations targeting big tech companies. 
    OpenAI has been lobbying against these measures, claiming they will stifle innovation. 
    Meanwhile, consumer advocacy groups praise the FTC's efforts to protect users from 
    potential AI harms. The new rules are seen as a necessary step to prevent tech giants 
    from monopolizing the AI market.
    """
    
    # Create analysis request
    request = analyzer.create_analysis_request(
        doc_id="sample_001",
        title="FTC Announces New AI Regulations",
        text=sample_text,
        optional_known_actors=["FTC", "OpenAI"]
    )
    
    # Generate different types of prompts
    unified_prompt = analyzer.get_unified_prompt(request)
    entities_prompt = analyzer.get_entity_relationships_prompt(request)
    
    print(f"Generated unified prompt ({len(unified_prompt)} characters)")
    print(f"Generated entities prompt ({len(entities_prompt)} characters)")
    
    # Show a snippet of the unified prompt
    print("\nUnified Prompt Preview:")
    print(unified_prompt[:500] + "..." if len(unified_prompt) > 500 else unified_prompt)

def example_batch_processing():
    """Example of batch processing multiple documents."""
    print("\n=== Batch Processing Example ===")
    
    # Create sample documents
    documents = [
        DocumentRecord(
            doc_id="doc_001",
            title="AI Safety Guidelines",
            text="NIST released comprehensive AI safety guidelines. OpenAI supports these measures while Meta expressed concerns about implementation costs.",
            actors=["NIST", "OpenAI", "Meta"]
        ),
        DocumentRecord(
            doc_id="doc_002", 
            title="Congressional AI Hearing",
            text="Congress held hearings on AI regulation. Tech leaders testified about the need for balanced oversight that promotes innovation.",
            actors=["Congress"]
        )
    ]
    
    # Initialize task manager
    config = BatchAnalysisConfig(
        mode="unified",
        max_workers=2,
        validate_results=True
    )
    task_manager = AnalysisTaskManager(config)
    
    # Process documents
    job_ids = task_manager.process_document_batch(documents, mode="entities")
    print(f"Created {len(job_ids)} analysis jobs")
    
    # Execute jobs
    results = task_manager.execute_batch_jobs(job_ids)
    successful = sum(1 for success in results.values() if success)
    print(f"Completed {successful}/{len(job_ids)} jobs successfully")
    
    # Get statistics
    stats = task_manager.get_job_statistics()
    print(f"Job Statistics: {json.dumps(stats, indent=2)}")

def example_pipeline_integration():
    """Example of data pipeline integration."""
    print("\n=== Pipeline Integration Example ===")
    
    integrator = DataPipelineIntegrator()
    
    # Create sample CSV data
    sample_csv_path = Path("sample_documents.csv")
    with open(sample_csv_path, 'w', encoding='utf-8') as f:
        f.write("doc_id,title,text,actors\n")
        f.write('doc1,"AI Policy Brief","Government agencies are developing AI oversight frameworks. The Department of Commerce leads coordination efforts.","[\"Department of Commerce\"]"\n')
        f.write('doc2,"Tech Industry Response","Industry leaders argue for self-regulation instead of government mandates. They claim innovation will be hindered.","[\"Industry Leaders\"]"\n')
    
    # Load documents from CSV
    documents = list(integrator.load_from_csv(sample_csv_path))
    print(f"Loaded {len(documents)} documents from CSV")
    
    # Generate prompts
    prompts = integrator.create_batch_prompts(documents, mode="portrayals")
    print(f"Generated {len(prompts)} analysis prompts")
    
    # Clean up
    sample_csv_path.unlink()

def example_workflow_orchestration():
    """Example of high-level workflow orchestration."""
    print("\n=== Workflow Orchestration Example ===")
    
    # Sample documents for analysis
    documents = [
        DocumentRecord(
            doc_id="policy_001",
            title="AI Executive Order",
            text="The President signed an executive order establishing new AI safety requirements. Tech companies must comply with testing and reporting standards.",
            actors=["President", "Tech Companies"]
        ),
        DocumentRecord(
            doc_id="industry_001", 
            title="Industry Pushback",
            text="Technology associations filed legal challenges against the new regulations, arguing they exceed executive authority and will harm competitiveness.",
            actors=["Technology Associations"]
        )
    ]
    
    # Initialize workflow
    workflow = AnalysisWorkflow()
    
    # Run comparative analysis
    results = workflow.comparative_analysis(
        documents, 
        modes=["entities", "portrayals", "issues"]
    )
    
    print(f"Comparative analysis completed for {len(results)} modes")
    for mode, mode_results in results.items():
        print(f"  {mode}: {len(mode_results)} document results")

def example_validation():
    """Example of result validation."""
    print("\n=== Validation Example ===")
    
    analyzer = DocumentAnalyzer()
    
    # Sample valid JSON response
    valid_response = json.dumps({
        "doc_id": "test_001",
        "title": "Test Document",
        "actors": [
            {
                "name": "FTC",
                "stakeholder_category": "Government",
                "notes": None
            }
        ],
        "relationships": {
            "skipped": True,
            "skip_reason": "No relationships found",
            "items": []
        },
        "portrayals": {
            "skipped": True,
            "skip_reason": "No portrayals found", 
            "items": [],
            "shifts": {
                "angel_shift_present": False,
                "devil_shift_present": False,
                "angel_shift_evidence": None,
                "devil_shift_evidence": None
            }
        },
        "issue_scope": {
            "skipped": True,
            "skip_reason": "No issue scope found",
            "items": []
        },
        "causal_mechanisms": {
            "skipped": True,
            "skip_reason": "No causal mechanisms found",
            "items": []
        },
        "ai_decisions": [
            {
                "action": "none",
                "actor": None,
                "reasoning": "No changes needed"
            }
        ]
    })
    
    # Validate the response
    is_valid, error, data = analyzer.validate_analysis_result(valid_response, "Sample document text")
    print(f"Validation result: {'VALID' if is_valid else 'INVALID'}")
    if error:
        print(f"Validation error: {error}")
    
    # Example with invalid JSON
    invalid_response = '{"doc_id": "test", "invalid": true'  # Missing closing brace
    is_valid, error, data = analyzer.validate_analysis_result(invalid_response, "Sample text")
    print(f"Invalid JSON validation: {'VALID' if is_valid else 'INVALID'}")
    if error:
        print(f"Error: {error}")

def example_directory_processing():
    """Example of processing documents from a directory.""" 
    print("\n=== Directory Processing Example ===")
    
    # Create sample directory structure
    sample_dir = Path("sample_documents")
    sample_dir.mkdir(exist_ok=True)
    
    # Create sample files
    (sample_dir / "doc1.txt").write_text("The FDA is reviewing AI applications in healthcare. Medical device companies are adapting to new requirements.")
    (sample_dir / "doc2.txt").write_text("Universities are establishing AI ethics boards. Research institutions collaborate on responsible AI development.")
    
    # Process directory
    task_manager = AnalysisTaskManager()
    job_ids = task_manager.process_from_directory(
        sample_dir,
        mode="causal",
        execute=True
    )
    
    print(f"Processed {len(job_ids)} documents from directory")
    
    # Export results
    results_file = Path("directory_analysis_results.json")
    task_manager.export_results(results_file)
    print(f"Results exported to {results_file}")
    
    # Clean up
    import shutil
    shutil.rmtree(sample_dir)
    if results_file.exists():
        results_file.unlink()

def run_all_examples():
    """Run all example functions."""
    examples = [
        example_basic_usage,
        example_batch_processing,
        example_pipeline_integration,
        example_workflow_orchestration,
        example_validation,
        example_directory_processing
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"Error in {example_func.__name__}: {str(e)}")
        print("-" * 50)

if __name__ == "__main__":
    print("Document Analysis Framework - Examples")
    print("=" * 50)
    run_all_examples()
    print("\nAll examples completed!")