STATEMENT OF <PERSON><PERSON><PERSON>EW SAG, PROFESSOR OF LAW, ARTIFICIAL 
    INTELLIGENCE, MACHINE LEARNING, AND DATA SCIENCE, EMORY 
           UNIVERSITY SCHOOL OF LAW, ATLANTA, GEORGIA

    Professor <PERSON><PERSON>. Chair <PERSON><PERSON>, Ranking Member <PERSON>, Members 
of the Subcommittee, thank you for the opportunity to testify 
here today. I am a professor of law in AI, machine learning, 
and data science at Emory University, where I was hired as part 
of Emory's AI Humanity Initiative.
    Although we are still a long way from the science fiction 
version of artificial general intelligence that thinks, feels, 
and refuses to open the pod bay doors, recent advances in 
machine learning and artificial intelligence have captured the 
public's attention and apparently lawmakers' interest.
    We now have large language models, or LLMs, that can pass 
the bar exam, carry on a conversation, create new music and new 
visual art. Nonetheless, copyright law does not and should not 
recognize computer systems as authors. Even where an AI 
produces images, text, or music that is indistinguishable from 
human authored works, it makes no sense to think of a machine 
learning program as the author.
    The Copyright Act rightly reserves copyrights for original 
works of authorship. As the Supreme Court explained long ago in 
the 1884 case of <PERSON>ow-<PERSON>, authorship entails 
original, intellectual conception. An AI can't produce a work 
that reflects its own original intellectual conception because 
it has none.
    Thus, when AI models produce content with little or no 
human oversight, there is no copyright in those outputs. 
However, humans using AI as tools of expression may claim 
authorship if the final form of the work reflects their 
original intellectual conception in sufficient detail. And I 
have elaborated in my written submissions how this will depend 
on the circumstances.
    Training generative AI on copyrighted works is usually fair 
use because it falls into the category of non-expressive use. 
Courts addressing technologies such as reverse engineering, 
search engines, and plagiarism detection software have held 
that these non-expressive uses are fair use. These cases 
reflect copyright's fundamental distinction between protectable 
original expression and unprotect-able facts, ideas, and 
abstractions.
    Whether training an LLM is in non-expressive use depends 
ultimately on the outputs of the model. If an LLM is trained 
properly and operated with appropriate safeguards, its outputs 
will not resemble its inputs in a way that would trigger a 
copyright liability. Training such an LLM on copyrighted works 
would thus be justified under current fair use principles.
    It is important to understand that generative AI are not 
designed to copy original expression. One of the most common 
misconceptions about generative AI is the notion that the 
training data is somehow copied into the model. Machine 
learning models are influenced by the data. They would be 
pretty useless without it. But they typically don't copy the 
data in any literal sense.
    So rather than thinking of an LLM as copying the training 
data like a scribe in a monastery, it makes more sense to think 
of it as learning from the training data like a student. If an 
LLM like GPT3 is working as intended, it doesn't copy the 
training data at all. The only copying that takes place is when 
the training corpus is assembled and pre-processed, and that is 
what you need a fair use justification for. Whether a 
generative AI produces truly new content or simply conjures up 
an infringing cut and paste of works in the training data 
depends on how it is trained.
    Accordingly, companies should adopt best practices to 
reduce the risk of copyright infringement and other related 
harms, and I have elaborated on some of these best practices in 
my written submission. Failure to adopt best practices may 
potentially undermine claims of fair use.
    Generative AI does not, in my opinion, require a major 
overhaul of the U.S. copyright system at this time.
    If Congress is considering new legislation in relation to 
AI and copyright, that legislation should be targeted at 
clarifying the application of existing fair use jurisprudence, 
not overhauling it.
    Israel, Singapore, and South Korea have recently 
incorporated fair use into their copyright statutes because 
these countries recognize that the flexibility of the fair use 
doctrine gives U.S. companies and U.S. researchers a 
significant competitive advantage.
    Several other jurisdictions, most notably Japan, the United 
Kingdom, and the European Union, have specifically adopted 
exemptions for text data mining that allow use of copyrighted 
works as training for machine learning and other purposes.
    Copyright law should encourage the developers of generative 
AI to act responsibly. However, if our laws become overly 
restrictive, then corporations and researchers will simply move 
key aspects of technology development overseas to our 
competitors.
    Thank you very much.