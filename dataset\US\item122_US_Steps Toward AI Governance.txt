Steps Toward AI Governance

Technical Limitations and Constraints on Effective AI Governance
External Models Can Be Difficult to Evaluate
The EqualAI Badge Community agreed that one of the most common technical challenges
companies face when evaluating and testing AI models is not knowing how rigorously external
models have been evaluated. Specifically, externally acquired models might have undergone
different testing and evaluation from those developed in-house. This knowledge gap about
external models stems from a lack of transparency regarding their development process,
including their initial design and the data they were trained on. Furthermore, participants noted
that external models would not have been tested against their organization’s use cases nor
adapted to their needs and preferences. In contrast, customized testing and adjustments would
have been possible for internally developed models or AI models procured and deployed after
modifications, such as fine-tuning.
Prioritizing Evaluation of High-Risk Use Cases
Differing use cases and risk levels present a significant technical challenge for companies in
prioritizing model testing and evaluations. Organizations must carefully identify which use cases
pose the highest risks and allocate their limited resources for testing and evaluation accordingly.
While the definition of high-risk use cases varies, the EqualAI Badge Community broadly agreed
that the highest risks include scenarios that could lead to bodily harm, financial loss, release of
sensitive nonpublic data, human resources (HR) violations, illegal outcomes, or denied
opportunities. Defensive applications of AI, such as those used for cybersecurity, are viewed as a
distinct category. Additionally, participants believed that generative AI could carry particularly
high risks, necessitating more-rigorous assessments of such use cases.
However, even if risk has been properly profiled, it might still be difficult to develop
standardized evaluation metrics that capture the vulnerabilities of a generative AI system, given
the diversity and stochasticity of its outputs. Participants pointed toward more-flexible evaluation
frameworks, such as red teaming or active user feedback, as mechanisms to identify failure
modes from generative systems that traditional metrics might not capture.
Organizational Limitations and Constraints on Effective AI Governance
Misaligned Incentives Might Slow or Impede AI Governance
Participants highlighted how misaligned organizational goals can create disincentives for
investing the significant resources required to implement appropriate AI processes. For instance,
sales and engineering teams might feel pressure to deliver products quickly, making it
challenging to integrate AI best practices, such as documentation for transparency, into an
already high-pressure environment. Gaining buy-in for AI governance initiatives is particularly
4
difficult when these practices are not legally mandated. Additionally, there are few incentives for
employees to voice concerns, and whistleblower protections are lacking. In such an environment,
risks can be overlooked. A changing regulatory landscape, however, could significantly shift this
risk environment. For example, new state-level or international regulations could alter incentive
structures for AI governance. As new regulations emerge, research will be needed to understand
their potential impact.
Company Culture Sets the Tone
Company culture is a key component of AI governance. The EqualAI Badge Community
emphasized that cultivating a supportive culture is essential for upholding AI principles,
requiring time and commitment to establish effective norms. Shifting the perception of AI
governance from that of an unnecessary burden to an essential asset is crucial for teams to
achieve their goals efficiently.
Teams must recognize the value of AI governance, including strengthening business
partnerships, preventing costly errors, fostering thought leadership, and attracting top talent with
relevant skills. Conversely, a lack of effective AI governance could result in legal liabilities,
reputational penalties, and/or financial penalties.
The EqualAI Badge Community agreed that an AI governance team needs authority to have
impact. As they put it, one factor that contributes to the perception that AI governance teams are
“not critical” is when they lack “no-go” authority or a “kill switch” to halt product releases or
ongoing processes. Typically, concerns must be validated by legal and compliance teams who do
possess such authority before action is taken to slow or stop operations. In general, the authority
to restrict a product release might need a clear, impending harm to justify the cost to agility, and
the absence of AI-specific laws and regulations in the United States and elsewhere might make it
harder to demonstrate these harms.
Leadership Buy-In Is Essential for Sustainable Governance
Leadership support is crucial for fostering an organizational culture that embraces AI
governance. While initiatives can start from the bottom up, true sustainability hinges on buy-in
from top leadership. This support should extend beyond a single leader to encompass the entire
board and C-suite executives. Moreover, given the potential for leadership turnover, securing
buy-in from multiple stakeholders is essential for long-term sustainability.
Employee Buy-In Is Crucial
According to participants, a significant challenge in adopting AI governance can stem from
employee fears of job displacement, which could foster a culture of hesitance toward AI
adoption. This resistance could be exacerbated by a lack of AI literacy throughout the
organization. Training employees on AI is challenging because training is likely most effective
when provided to those who specifically need such training to do their jobs, such as those who
5
use AI tools or who make strategic decisions about investing in AI technology. Measuring the
success of any AI training presents a related challenge and starts with understanding what level
of AI literacy is needed for a given role.
Increasing employee comfort with AI is also dependent on organizational goals and
incentives. If there are few opportunities for employees to learn about AI governance and best
practices, this could contribute to a lack of consensus about how AI systems should be
implemented or governed.
Vendor Relations Should Be Strengthened
Productive vendor relationships can be hindered by an overall lack of technical knowledge
about a given AI model (e.g., model architecture, testing and validation results). Procurement
teams might not know which technical questions to ask about AI governance, while vendors
might be ill-equipped to provide the necessary insights on such crucial aspects as metrics and
model transparency. The risk that a generative AI system poses, for example, might depend
heavily on the use case and environment in which it is deployed, further complicating matters.
Therefore, vendors could be caught in situations in which they must develop a suite of metrics
that are applicable to consumers across business domains while being specific and actionable
enough for the customized use cases and risk profiles of an individual customer. This gap in
foundational understanding and consistent inquiry leads to miscommunication and missed
opportunities for ensuring effective AI development and testing.
Recommendations
The EqualAI Badge Community proposed several solutions to address the challenges of
implementing AI governance, encompassing recommendations for both companies and
government bodies.
Recommendations for Companies
Catalog AI Use Cases
Companies should maintain a centralized catalog of AI tools and their applications, and the
catalog should be regularly updated to track use across the organization. This catalog should
document key specifications and facilitate risk assessment, prioritizing use cases based on such
factors as potential financial loss, HR violations, illegal outcomes, or bodily harm. By clarifying
uses and their risk levels, organizations can promote transparency and informed decisionmaking
regarding AI.
Standardize Vendor Questions
Developing a standardized set of relevant questions for vendors would enable companies to
align on vendor evaluations that are based on standardized metrics. Vendors would be aware
6
of—and incentivized to meet—key expectations. This approach can facilitate better integration
of aligned practices into vendor model development, promoting AI governance across business
relationships.
Create an AI Information Tool
Implementing an internal information tool, such as a chatbot, that would provide a
company’s employees with clear answers to AI governance questions could serve as a valuable
resource. This chatbot could be trained with relevant information from government, industry, and
nonprofit sources and could be tailored to the company’s needs and use cases.
Foster Multistakeholder Engagement
Companies should prioritize both internal and external engagement regarding their AI
practices. Internally, this involves securing C-suite support and fostering a culture of AI
governance through education and open communication. Externally, organizations should solicit
feedback from a broad set of stakeholders, particularly unanticipated end users and those from
historically marginalized communities, to understand and mitigate the impacts of AI
deployments that might not have been captured in the development process. Additionally,
thoroughly documenting these actions throughout the AI life cycle will enhance transparency and
aid in cross-enterprise and regulatory discussions.
Leverage Existing Processes
Organizations can apply such established processes as crisis management to handle AIrelated incidents (e.g., data leaks) and technical risk management (e.g., code reviews, stress
testing) to support AI governance. This approach encourages efficiency by integrating AI
governance practices into existing frameworks rather than creating new ones.
Recommendations for the Federal Government
Promote a Consistent Approach
According to summit participants, the federal government should legislate, foster, or
incentivize the establishment of a consistent regulatory framework that would help organizations
navigate the complexities and standardize the management of AI governance across various
states and international jurisdictions. This might involve clarifying existing laws and providing
new regulations and laws that govern the use of AI tools specifically. The EqualAI Badge
Community highlighted a desire for governments to balance swift regulatory action with the
promotion of innovation as AI technologies continue to evolve.
7
Conclusion
The 2024 EqualAI summit underscored the critical importance of fostering a collaborative
environment for effective AI governance. As the adoption of AI technologies accelerates,
organizations must move beyond isolated efforts to implement, evaluate, and manage their AI
systems. Going forward, organizations should engage in shared learning to effectively tackle the
complex AI-related challenges that they face. Summit discussions highlighted a broad array of
approaches, tools, and frameworks that are being employed but also revealed significant gaps
and limitations in technical evaluation, organizational culture, and employee engagement.
To appropriately navigate the fast-moving landscape of AI, organizations need to prioritize
building a culture that values best practices and is supported by leadership across all levels. This
effort entails investing in AI literacy, standardizing vendor expectations, and aligning
organizational goals with AI governance principles. By fostering an environment in which
knowledge and best practices can be shared, businesses can not only mitigate risks but also
unlock the full potential of AI technologies.
Looking ahead, the insights from the third EqualAI summit offer considerations for
organizations aiming to implement effective structures for AI governance. This imperative is not
merely a regulatory need but a strategic imperative that can enhance innovation, build trust, and
foster sustainable outcomes. Key recommendations from the summit can support best practices
to incorporate and standardize in companies across the country and globe, such as aligning
vendor questions, creating an AI information tool, and maintaining a centralized catalog of AI
use cases. Fostering multistakeholder engagement and leveraging existing organizational
processes on which to establish AI governance practices are key elements of ensuring long-term
success in this critical effort.
Building a set of resources or a community to share best practices and lessons learned is,
thus, critical to sustaining AI governance efforts. Several summit participants mentioned that,
from their perspectives, one of the most-valuable experiences was the opportunity to meet and
share insights with peers working on corporate AI governance. The journey toward widespread
effective use of AI is ongoing, and collaboration across disciplines and industry sectors will be
essential in shaping a future in which AI serves as a positive force for all of society. 